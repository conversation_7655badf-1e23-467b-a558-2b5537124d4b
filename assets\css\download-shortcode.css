/**
 * SolidCAM Download Shortcode Styles
 * Version: 1.1
 */

/* Download Item */
.solidcam-dl-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 5px 0;
    text-decoration: none;
    color: inherit;
    border-bottom: 1px dashed var(--e-global-color-8807009, #DEE2E6);
    transition: all 0.2s ease;
}
.dce-acf-repeater-item:last-child .solidcam-dl-item {
    padding-bottom: 0;
}

.dce-acf-repeater-item:last-child .solidcam-dl-item {
    border-bottom: none;
}

.solidcam-dl-item:hover {
    text-decoration: none;
	background: #f5f5f5;
}

.solidcam-dl-item:hover .dl-name {
    color: var(--e-global-color-primary, #850F0D);
}

/* Icon Wrapper */
.dl-icon-wrap {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border-radius: 6px;
    flex-shrink: 0;
}

.dl-icon {
    width: 16px;
    height: 16px;
    fill: var(--e-global-color-secondary, #666);
    transition: fill 0.2s ease;
}

.solidcam-dl-item:hover .dl-icon {
    fill: var(--e-global-color-primary, #850F0D);
}

/* Info Section */
.dl-info {
    flex: 1;
    min-width: 0; /* Für Text-Overflow */
	line-height: 1em;
}

.dl-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--e-global-color-text, #333);
    line-height: 1.4;
    transition: color 0.2s ease;
}

/* Meta Information */
.dl-meta {
    font-size: 12px;
    color: var(--e-global-color-2525440, #888);
    margin-top: 0;
}

.dl-meta .filename {
    color: var(--e-global-color-2525440, #999);
}

.dl-meta .separator {
    margin: 0 4px;
    opacity: 0.5;
}

.dl-meta .filetype {
    font-weight: 600;
    text-transform: uppercase;
}

/* Container Reset */
.dce-acf-repeater-item {
    margin: 0 !important;
}

/* Mobile Optimierung */
@media (max-width: 480px) {
    .solidcam-dl-item {
        padding: 10px 0;
    }
    
    .dl-name {
        font-size: 13px;
    }
    
    .dl-meta {
        font-size: 11px;
    }
}

/* Focus State für Accessibility */
.solidcam-dl-item:focus {
    outline: 2px solid var(--e-global-color-primary, #850F0D);
    outline-offset: 2px;
}
