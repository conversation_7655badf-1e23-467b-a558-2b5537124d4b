<?php
/**
 * Recommended way to include parent theme styles.
 * (Please see http://codex.wordpress.org/Child_Themes#How_to_Create_a_Child_Theme)
 *
 */  

add_action( 'wp_enqueue_scripts', 'hello_elementor_child_assets' );
function hello_elementor_child_assets() {
    // Enqueue parent theme's stylesheet
    wp_enqueue_style( 'parent-style', get_template_directory_uri() . '/style.css' );

    // Enqueue child theme's stylesheet with dependency on parent style
    wp_enqueue_style( 'child-style', get_stylesheet_directory_uri() . '/style.css', array('parent-style'), '1.1.2' );
    wp_enqueue_style( 'select2-style', get_stylesheet_directory_uri() . '/assets/css/select2.min.css', array(), wp_get_theme()->get('Version') );

    // Enqueue custom JavaScript file from child theme
    wp_enqueue_script( 'select2-js', get_stylesheet_directory_uri() . '/assets/js/select2.min.js', array(), null, true );
    wp_enqueue_script( 'child-theme-js', get_stylesheet_directory_uri() . '/assets/js/main.js', array(), '1.1.1', true );
	
	// Get the `core_account` page ID from the options
	$um_options = get_option('um_options');
	$core_account_page_id = isset($um_options['core_account']) ? $um_options['core_account'] : null;
	
	// Check if we are on the page matching the `My SolidCAM Page` ID
	// if (is_page($core_account_page_id)) {
		
		wp_enqueue_script('my-solidcam-script', get_stylesheet_directory_uri() . '/assets/js/my-solidcam-script.js', ['jquery'], '1.0.5', true);
		wp_enqueue_script('versioning-module', get_stylesheet_directory_uri() . '/assets/js/versioning-module.js', ['jquery'], '1.0.1', true);
		wp_localize_script('my-solidcam-script', 'mss_ajax', 
			[
				'ajax_url' => admin_url('admin-ajax.php'),
				'nonce' => wp_create_nonce('mss_ajax_nonce'),
				'login_nonce' => wp_create_nonce('um_ajax_login_nonce'),
				'reset_nonce' => wp_create_nonce('um_ajax_reset_password_nonce'),
				'addon_nonce' => wp_create_nonce('mss_addon_nonce'),
				'training_nonce' => wp_create_nonce('training_courses_nonce'),
				'remove_license' => __('Please select a license to remove.', 'solidcam'),
				'select_license' => __('Please select a valid license.', 'solidcam'),
				'software_key' => __('Please enter a Dongle/Software-Key.', 'solidcam'),
				'software_authorize' => __('You must confirm that you are authorized to register this Dongle/Software-Key.', 'solidcam'),
				'module_card_heading' => __('Request Sent', 'solidcam'),
				'module_card_text' => __('Your responsible contact person has been informed of the request and will get back to you as soon as possible.', 'solidcam'),
				
			]
		);
		
		// In your PHP file where you output the JavaScript
		$languages = array();
		if (function_exists('icl_get_languages')) {
			$wpml_languages = icl_get_languages('skip_missing=0');
			foreach ($wpml_languages as $lang_code => $lang) {
				$languages[$lang_code] = $lang['translated_name'];
			}
			$languages['el'] = __('Greek', 'solidcam');
		}
		wp_localize_script('versioning-module', 'download_module', 
			[
				'languages' => $languages,
				'current_language' => apply_filters( 'wpml_current_language', NULL )
			]
		);
	// }
	
	wp_enqueue_script('calendar-widget-js', get_stylesheet_directory_uri() . '/assets/js/calendar-widget.js', ['jquery'], false, true);
	wp_localize_script('calendar-widget-js', 'calendar_ajax', ['ajax_url' => admin_url('admin-ajax.php')]);
	
	wp_register_script(
		'training-courses-widget',
		get_stylesheet_directory_uri() . '/assets/js/training-courses-widget.js',
		['jquery'],
		'1.0.0',
		true
	);
	
	wp_register_style(
		'training-courses-widget',
		get_stylesheet_directory_uri() . '/assets/css/training-courses-widget.css',
		[],
		'1.0.0'
	);
}

function is_current_user_administrator() {
	$current_user = wp_get_current_user();
	if( $current_user ){
		return in_array( 'administrator', $current_user->roles );
	}
}

add_filter( 'show_admin_bar', 'is_current_user_administrator', 99 );

function enqueue_my_solidcam_admin_script_on_profile( $hook ) {
	if ( $hook === 'profile.php' || $hook === 'user-edit.php' ) {
		// Enqueue the script
		wp_enqueue_script(
			'my-solidcam-admin', // Handle name
			get_stylesheet_directory_uri() . '/assets/js/my-solidcam-admin.js',
			[ 'jquery' ], // Dependencies
			'1.0.0', // Version
			true // Load in the footer
		);
	}
}
add_action( 'admin_enqueue_scripts', 'enqueue_my_solidcam_admin_script_on_profile' );



// Add category option in the Elementor archive widget's skin
add_action('elementor/widget/archive-posts/skins_init', 'override_post_skins_over');

/**
 * @param $widget
 * @return void
 */
function override_post_skins_over($widget)
{
	include get_stylesheet_directory() . '/skins/custom-archive-post-skin.php';
	$widget->add_skin(new Skin_Base_Custom_Archive_Post($widget));
}

add_action('elementor/widget/posts/skins_init', 'override_post_skins_posts');
/**
 * @param $widget
 * @return void
 */
function override_post_skins_posts($widget)
{
	include get_stylesheet_directory() . '/skins/custom-posts-skin.php';
	include get_stylesheet_directory() . '/skins/custom-posts-event-skin.php';
	
	$widget->add_skin(new Skin_Base_Custom_Posts($widget));
	$widget->add_skin(new Skin_Base_Custom_Posts_Event($widget));
}

// Register new conditions

/* add_action( 'elementor/display_conditions/register', function( $conditions_manager ) {
	include get_stylesheet_directory() . '/conditions/api-conditions-my-solidcam.php';
	
	$conditions_manager->register_condition_instance( new Api_User_Data_Condition());
} ); */


function rdsn_acf_repeater_collapse() {
?>
<style>.acf-repeater .acf-row > .acf-row-handle .acf-icon {display:block !important;}</style>
<!-- <style id="rdsn-acf-repeater-collapse">.acf-repeater .acf-table {display:none;}</style>
<script type="text/javascript">
	jQuery(function($) {
		// Select the parent element
		var $tbody = $('.acf-field .acf-repeater tbody');

		// Get all first-level child divs with class 'acf-row' and add the '-collapsed' class
		$tbody.children('.acf-row').addClass('-collapsed');

		// Remove the style element
		$('#rdsn-acf-repeater-collapse').detach();
	});
</script> -->
<?php
}
add_action('acf/input/admin_head', 'rdsn_acf_repeater_collapse');



function language_acf_load_field( $field ) {
	// Check if WPML is active and the function exists
	if ( function_exists( 'icl_get_languages' ) ) {
		// Get all the active languages
		$languages = icl_get_languages('skip_missing=0');
		
		if ( !empty( $languages ) ) {
			$field['choices'] = array('' => 'Choose Language'); // Reset choices
			
			// Loop through languages and add them as choices
			foreach ( $languages as $lang_code => $lang ) {
				$field['choices'][ $lang_code ] = $lang['translated_name'];
			}
		}
	} else {
		// If WPML is not available, start with empty choices
		$field['choices'] = array('' => 'Choose Language');
	}
	
	// Add Greece (Greek) as a hardcoded option
	$field['choices']['el'] = __('Greek', 'solidcam');
	
	// Return the field
	return $field;
}

// Hook into the acf/load_field filter
add_filter('acf/load_field/name=language', 'language_acf_load_field');


function bulk_softwares_acf_save_bulk_downloads($post_id) {
	// Check if we're saving the options page
	if ($post_id !== 'options') {
		return;
	}
	
	// Fetch existing software repeater field values
	$softwares = get_field('softwares', 'option');
	
	/* echo '<pre>';
	print_r($softwares);
	die; */
	
	// Process the downloads
	$updated_softwares = process_software_downloads($softwares);
	
	/* // Fetch languages dynamically from WPML
	$languages = [];
	if (function_exists('icl_get_languages')) {
		$wpml_languages = icl_get_languages('skip_missing=0');
		if (!empty($wpml_languages)) {
			foreach ($wpml_languages as $lang_code => $lang) {
				$languages[$lang['translated_name']] = $lang_code;
			}
		}
	}
	
	// Loop through each software item and patch version
	foreach ($softwares as $software) {
		foreach ($software['major_versions'] as $software_item) {
			foreach ($software_item['patch_versions'] as &$patch_version) {
				// Check if the bulk_add_downloads field is set and not empty
				if (isset($patch_version['bulk_add_downloads']) && !empty($patch_version['bulk_add_downloads'])) {
					$bulk_add_downloads = sanitize_text_field($patch_version['bulk_add_downloads']);
	
					// Split the input into lines
					$lines = explode("\n", $bulk_add_downloads);
					
					if( count($lines) < 2 ){
						$lines = preg_split('/\s+/', $bulk_add_downloads);
					}
					// Initialize the downloads array
					$downloads = [];
	
					// Loop through each line
					foreach ($lines as $line) {
						$url = trim($line);
						$language_found = false;
	
						// Determine the language from the URL
						foreach ($languages as $language_name => $language_code) {
							if (stripos($url, $language_name) !== false) {
								// Add to the downloads array
								$downloads[] = [
									'url' => $url,
									'language' => $language_code
								];
								$language_found = true;
								break;
							}
						}
	
						// If no language is found, you can handle it as needed, e.g., log an error or assign a default language
						if (!$language_found) {
							// Handle the case where the language is not found
							// Example: assign a default language or log an error
							$downloads[] = [
						    	'url' => $url,
						    	'language' => 'unknown'
							];
						}
					}
	
					// Update the downloads repeater field for this patch version
					$patch_version['downloads'] = $downloads;
	
					// Clear the bulk_add_downloads field
					$patch_version['bulk_add_downloads'] = '';
				}
			}
		}
	} */

	// Update the repeater field with the new structure
	update_field('softwares', $updated_softwares, 'option');
}
add_action('acf/save_post', 'bulk_softwares_acf_save_bulk_downloads', 20);

function process_software_downloads($softwares) {
	// Get WPML languages
	$languages = [];
	if (function_exists('icl_get_languages')) {
		$wpml_languages = icl_get_languages('skip_missing=0');
		foreach ($wpml_languages as $lang_code => $lang) {
			$languages[$lang['translated_name']] = $lang_code;
			
			// Add common variations
			if ($lang_code === 'zh-hans') {
				$languages['ChineseSimplified'] = $lang_code;
			} elseif ($lang_code === 'zh-hant') {
				$languages['ChineseTraditional'] = $lang_code;
			}
		}
	}

	foreach ($softwares as &$software) {
		if (!isset($software['major_versions']) || !is_array($software['major_versions'])) {
			continue;
		}

		foreach ($software['major_versions'] as &$major_version) {
			if (!isset($major_version['patch_versions']) || !is_array($major_version['patch_versions'])) {
				continue;
			}

			foreach ($major_version['patch_versions'] as &$patch_version) {
				if (empty($patch_version['bulk_add_downloads'])) {
					continue;
				}

				// Split bulk downloads into lines
				$lines = preg_split('/\r\n|\r|\n/', trim($patch_version['bulk_add_downloads']));
				
				// Initialize or get existing downloads array
				$downloads = isset($patch_version['downloads']) ? $patch_version['downloads'] : [];

				foreach ($lines as $line) {
					$url = trim($line);
					if (empty($url)) continue;

					$language_code = get_language_code_from_url($url, $languages);
					
					// Add to downloads array if URL is valid
					if (filter_var($url, FILTER_VALIDATE_URL)) {
						$downloads[] = [
							'url' => $url,
							'language' => $language_code
						];
					}
				}

				// Update downloads and clear bulk field
				$patch_version['downloads'] = array_values(array_unique($downloads, SORT_REGULAR));
				$patch_version['bulk_add_downloads'] = '';
			}
		}
	}

	return $softwares;
}

function get_language_code_from_url($url, $languages) {
	// Convert URL to lowercase for case-insensitive matching
	$url_lower = strtolower($url);
	
	// Extract filename from URL
	$filename = basename($url_lower);
	
	foreach ($languages as $language_name => $language_code) {
		// Create variations of language names for matching
		$variations = [
			strtolower($language_name),
			str_replace(' ', '', strtolower($language_name))
		];
		
		foreach ($variations as $variation) {
			if (strpos($filename, $variation) !== false) {
				return $language_code;
			}
		}
	}
	
	// Return empty if no language found
	return '';
}

//Function to add class to the menu
if ( ! function_exists( 'add_class_to_menu_item_with_children' ) ) {
    function add_class_to_menu_item_with_children($classes, $item, $args, $depth) {
        // Check if it's a top-level menu item with children
        if ( 0 === $depth && in_array( 'menu-item-has-children', $item->classes, true ) ) {
            $classes[] = 'primary-submanu-sidebar';
        }
    
        // Check if current page is an archive or a single post
        if (is_archive() || is_single()) {
            $classes[] = 'primary-submanu-sidebar';
        }
    
        return $classes;
    }
    add_filter('nav_menu_css_class', 'add_class_to_menu_item_with_children', 10, 4);
}



// Elementor Custom Widgets
if ( ! function_exists( 'register_solidcam_custom_widget' ) ) {
    function register_solidcam_custom_widget( $widgets_manager ) {
        
    	require_once( 'widget/simple-menu.php' );
    	$widgets_manager->register( new \Elementor_Nav_Menu_Widget() );
    	
    	require_once( 'widget/cam-parts-download.php' );
    	$widgets_manager->register( new \Elementor_Cam_Parts_Widget() );
    	
    	require_once( 'widget/event-calendar-view.php' );
    	$widgets_manager->register( new \Elementor_Calendar_Widget() );
		
		require_once( 'widget/addon-module-single-widget.php');
		$widgets_manager->register(new \Single_Addon_Module_Widget() );
    	
    	//Custom License Widget For Dashboard
    	require_once( 'widget/mysolidcam-license-widget.php');
        $widgets_manager->register(new \Custom_License_Widget() );
        
        //Custom License Updates Widget For Dashboard
    	require_once( 'widget/mysolidcam-license-updates-widget.php');
        $widgets_manager->register(new \Custom_License_Updates_Widget() );
        
        //Custom My Plan Widget For Dashboard
    	require_once( 'widget/mysolidcam-my-plan-widget.php');
        $widgets_manager->register(new \My_Plan_Widget() );
        
        //Custom Expiration Widget For Dashboard
    	require_once( 'widget/mysolidcam-expiration-widget.php');
        $widgets_manager->register(new \Expiration_Widget() );
        
        //Custom My Status Widget For Dashboard
    	require_once( 'widget/mysolidcam-my-status-widget.php');
        $widgets_manager->register(new \My_Status_Widget() );
		
		//Custom Download Widget For Dashboard
		require_once( 'widget/mysolidcam-download-widget.php');
		$widgets_manager->register(new \MySolidCam_Download_Widget() );
		
		
		//Custom User Profile Widget For Dashboard
		require_once( 'widget/mysolidcam-user-profile-widget.php');
		$widgets_manager->register(new \MySolidCam_User_Profile_Widget() );
		
		//German Training Widget
		require_once( 'widget/training-courses-widget.php');
		$widgets_manager->register(new \Training_Courses_Widget() );
    
    }
    add_action( 'elementor/widgets/widgets_registered', 'register_solidcam_custom_widget' );
}


if ( ! function_exists( 'register_elementor_custom_dynamic_tags' ) ) {
	function register_elementor_custom_dynamic_tags( $dynamic_tags ) {
		$tags = [
			'event-days-left'                  => 'Elementor_Custom_Event_Days_Left',
			'add-to-calendar'                  => 'Elementor_Custom_Add_To_Calendar',
			'user-dynamic-data'                => 'User_Dynamic_Data',
			'training-dynamic-data'            => 'Training_Dynamic_Data',
			'customer-reviews-rating'          => 'Customer_Reviews_Rating_Count',
			'partner-url-text'          	   => 'Partner_URL_Text',
			'event-start-end-dates'          	   => 'Elementor_Custom_Event_Dates',
		];

		foreach ( $tags as $file => $class ) {
			require_once "tags/{$file}.php";
			$dynamic_tags->register_tag( $class );
		}
	}
	add_action( 'elementor/dynamic_tags/register', 'register_elementor_custom_dynamic_tags' );
}


// All user API calls
require_once('api/user-api-data.php');

// Form Section Divider Field 
require_once ('salesforce/class-salesforce-user-group-service.php');
require_once('includes/form-fields/form-section-divider-field.php');
require_once('includes/form-customizations.php');
require_once('includes/video-customizations.php');
require_once('includes/dynamic-accordion.php');
require_once('includes/events-modifications.php');
require_once('includes/my-solidcam-modifications.php');
require_once('includes/user-authentication-typo3.php');
require_once('includes/training-ajax-handlers.php');


//WPML Langauge Dropdown Functionality
if ( ! function_exists( 'show_current_language_flag' ) ) {
    function show_current_language_flag() {
    	$language_name = '';
    	$flag_url = '';
        $current_language = apply_filters( 'wpml_current_language', NULL );
        $languages = apply_filters( 'wpml_active_languages', NULL, 'orderby=id&order=desc' );
      
        // Find the current language in the languages array
        if ( isset( $languages[ $current_language ] ) ) {
            $language_details = $languages[ $current_language ];
            $flag_url = $language_details['country_flag_url'];
        }
        // Output HTML
        ob_start();
        ?>
        <div class="current-language-flag">
            <?php if ( ! empty( $flag_url ) ) : ?>
                <img class="language-flag" src="<?php echo esc_url( $flag_url ); ?>" alt="Flag of <?php echo esc_attr( $language_name ); ?>">
            <?php endif; ?>
            <button class="language-button">
    			<img class="language-flag" src="<?php echo get_stylesheet_directory_uri() . '/assets/images/globe.svg'; ?>" alt="Globe Vector">
                	<?php echo __('Language', 'solidcam'); ?> 
    			<img class="language-flag" src="<?php echo get_stylesheet_directory_uri() . '/assets/images/chevron-down.svg'; ?>" alt="Chevron Down">
            </button>
        </div>
        <?php
        return ob_get_clean();
    }
    add_shortcode( 'current_language_flag', 'show_current_language_flag' );
}

// ACF Google Maps API
function my_acf_init() {
    acf_update_setting('google_api_key', 'AIzaSyByvhBUqNsXHdykGKLGxcSOuKPNTcWIgZA');
}
add_action('acf/init', 'my_acf_init');

// Get Post meta data in Twig wp_enqueue_block_template_skip_link()
if ( ! function_exists( 'solidcam_get_post_meta_data' ) ) {
    function solidcam_get_post_meta_data( $post_id, $post_meta_key ){
    	$fild_value = '';
    	if( function_exists('get_field') ){
    		$fild_value = get_field($post_meta_key, $post_id);
    	}
    	if( !$fild_value ){
    		$fild_value = get_post_meta( $post_id, $post_meta_key, true);
    	}
    	return $fild_value;
    }
    
    add_filter('get_post_meta_value_twig', 'solidcam_get_post_meta_data', 10, 2);
}


//Parent Filter Query 
if ( ! function_exists( 'sc_filter_parent_category' ) ) {
    function sc_filter_parent_category($args, $widgetData){
        $child_cat  = $args['child_of'];
        $args['parent'] = $child_cat;
        
    	return($args);
    	
    }
    add_filter("filter_parent_category", "sc_filter_parent_category",10,2);
}


//Parent Filter Query 
if ( ! function_exists( 'sc_show_filter_taxonomy' ) ) {
    function sc_show_filter_taxonomy($args, $widgetData){
        $child_cat  = $args['child_of'];
        $args['parent'] = $child_cat;
        
    	return($args);
    	
    }
    add_filter("show_filter_taxonomy", "sc_show_filter_taxonomy",10,2);
}



// Code for Stars filters 
if ( ! function_exists( 'modify_posts_query_with_stars_filter' ) ) {
    function modify_posts_query_with_stars_filter( $args ) {
        
        if ( isset( $_REQUEST['stars'] ) && $_REQUEST['stars'] != '' ) {
            $star_values = explode(',', $_REQUEST['stars']); // Convert comma-separated string to array


            if ( !isset( $args['meta_query'] ) ) {
                $args['meta_query'] = array( 'relation' => 'AND' );
            }

            // Modify query to support multiple star values
            $args['meta_query'][] = array(
                'key'     => 'customer_rating',
                'value'   => $star_values,  // Pass an array to support multiple values
                'compare' => 'IN',         // Use "IN" to match multiple values
                'type'    => 'NUMERIC',
            );
        }

        return $args;
    }

    add_filter( 'ue_modify_posts_query_args', 'modify_posts_query_with_stars_filter', 10 );
}

// if ( ! function_exists( 'modify_posts_query_with_stars_filter' ) ) {
//     function modify_posts_query_with_stars_filter( $args ) {
        
//         if( isset( $_REQUEST['stars'] ) && $_REQUEST['stars'] != '' ) {
//             $star_value = intval( $_REQUEST['stars'] ); // Convert the value to an integer

//             // Check if a meta_query already exists, if not, initialize it
//             if( !isset( $args['meta_query'] ) ) {
//                 $args['meta_query'] = array( 'relation' => 'AND' );
//             }

//             // Add the customer_rating filter to the meta_query
//             $args['meta_query'][] = array(
//                 'key'     => 'customer_rating',  // The custom field (meta key)
//                 'value'   => $star_value,        // The star value from the request
//                 'compare' => '=',                // Exact match
//                 'type'    => 'NUMERIC',          // Ensure it's treated as a number
//             );
//         }

//         return $args;
//     }

//     add_filter( 'ue_modify_posts_query_args', 'modify_posts_query_with_stars_filter', 10 );
// }


//Code to redirect template to child page of Empty Parent
if ( ! function_exists( 'redirect_parent_to_first_child' ) ) {
	function redirect_parent_to_first_child() {
		if (is_page() && !is_admin()) {
			global $post;
			
			// Get current language (WPML)
			$current_language = apply_filters('wpml_current_language', NULL);
			
			// Check if it's German language and the page slug is 'contact'
			if ($current_language === 'de' && $post->post_name === 'kontakt') {
				return; // Don't redirect German contact page
			}
	
			// Check if the current page has children (use get_posts to fetch child pages)
			$children = get_posts(array(
				'post_type'   => 'page',
				'posts_per_page' => 1,  // We only need the first child
				'post_parent' => $post->ID,
				'orderby'     => 'menu_order',
				'order'       => 'ASC'
			));
	
			// Check if there are any children and the parent page is empty
			if ($children && empty($post->post_content)) {
				// Redirect to the first child page
				$first_child = $children[0];
				wp_redirect(get_permalink($first_child->ID), 301);
				exit();
			}
		}
	}
	add_action('template_redirect', 'redirect_parent_to_first_child');
}


// function to Fetch posts based on Term Id for Document Post Taxonmy
if ( ! function_exists( 'getPostsForTerm' ) ) {
    function getPostsForTerm($term_id) {
    	$cat_id = get_queried_object_id();
        // Set up the arguments for WP_Query
        $args = array(
            'post_type' => 'document', // Specify your custom post type
            'tax_query' => array(
    	        array(
    	            'taxonomy' => 'software-version',
    	            'field'    => 'term_id',
    	            'terms'    => $cat_id,
    	        ),
                array(
                    'taxonomy' => 'document-category', // Replace with your actual taxonomy name
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            ),
            'posts_per_page' => -1, // Retrieve all posts
        );
    
        // Execute the query
        $query = new WP_Query($args);
        $posts = array();
    
        // Check if posts were found
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
    
                // Get ACF fields
                $description = get_field('description');
                $doc_link = get_field('doc_link');
                $part_link = get_field('part_link');
                $download_button_text = get_field('download_button_text');
                $part_button_text = get_field('part_button_text');
                $lock = get_field('lock'); 
                $notice = get_field('notice');
                $button_text_signup = get_field('button_text_signup');
                $button_url_signup = get_field('button_url_signup');
                
                // Add each post to the array
                $posts[] = array(
                    'ID' => get_the_ID(),
                    'title' => get_the_title(),
                    'link' => get_permalink(),
                    'featured_image' => get_the_post_thumbnail_url(get_the_ID(), 'full'), // Get featured image
                    'description' => $description,
                    'doc_link' => $doc_link,
                    'part_link' => $part_link,
                    'download_button_text' => $download_button_text,
                    'part_button_text' => $part_button_text,
                    'lock' => $lock,
                    'notice' => $notice,
                    'button_text_signup' => $button_text_signup,
                    'button_url_signup' => $button_url_signup,
                    
                );
            }
            // Reset post data
            wp_reset_postdata();
        }
        
        return $posts;
    }
    
    add_filter( 'get_post_documents_data', 'getPostsForTerm' );
}

if ( ! function_exists( 'sc_get_post_documents_count' ) ) {
    function sc_get_post_documents_count( $term_id ){
    	$cat_id = get_queried_object_id();
        // Set up the arguments for WP_Query
        $args = array(
            'post_type' => 'document', // Specify your custom post type
            'tax_query' => array(
    	        array(
    	            'taxonomy' => 'software-version',
    	            'field'    => 'term_id',
    	            'terms'    => $cat_id,
    	        ),
                array(
                    'taxonomy' => 'document-category', // Replace with your actual taxonomy name
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            ),
            'posts_per_page' => -1, // Retrieve all posts
        );
        
    	$query = new WP_Query($args);
    	$post_count = $query->found_posts;
    	wp_reset_postdata();
    	return $post_count;
    	
    }
    add_filter( 'get_post_documents_count', 'sc_get_post_documents_count');
}

// helper: convert any URL to the current WPML language version
if ( ! function_exists( 'sc_wpml_localize_url' ) ) {
	function sc_wpml_localize_url( $url, $lang = null ) {
		if ( empty( $url ) || is_wp_error( $url ) ) {
			return $url;
		}
		// only run if WPML is active
		if ( function_exists( 'apply_filters' ) && defined( 'ICL_SITEPRESS_VERSION' ) ) {
			if ( $lang === null ) {
				$lang = apply_filters( 'wpml_current_language', null );
			}
			$url = apply_filters( 'wpml_permalink', $url, $lang );
		}
		return $url;
	}
}

// function to Fetch get Categories of Software Version
if ( ! function_exists( 'sc_get_categories_by_parent' ) ) {
    function sc_get_categories_by_parent() {
      $terms = get_terms( array(
            'taxonomy' => 'software-version',
            'hide_empty' => false,
            'parent' => 0, 
            'orderby' => 'name', 
            'order' => 'DESC' 
        ));
        
		$current_lang = defined('ICL_SITEPRESS_VERSION')
		? apply_filters( 'wpml_current_language', null )
		: null;
		
        $categories_with_children = array();
		
         // Loop through each parent category and fetch its children
        foreach ($terms as $parent_term) {
            $child_terms = get_terms(array(
                'taxonomy' => 'software-version',
                'hide_empty' => false,
                'parent' => $parent_term->term_id,
            ));
			
            $children_with_details = array(); // New array to hold child details
            if( empty($child_terms) ){
				$categories_with_children[] = array(
					'parent' => $parent_term,
					'children' => $children_with_details // Store children with their links and images
				);
			} else{
				foreach ($child_terms as $child_term) {
					// Get the ACF image for each child term
					$image = get_field('category_image', 'software-version_' . $child_term->term_id); // Fetching the ACF image field
					
					// base link
					$link  = get_term_link( $child_term );
					
					// ensure link is localized for the current language (e.g. /de/, /fr/)
					$link  = sc_wpml_localize_url( $link, $current_lang );
					
					// Append child details
					$children_with_details[] = array(
						'name' => $child_term->name,
						'link' => $link, // Get link for the child term
						'image' => $image ? $image['url'] : '', // Image URL if it exists
						'id' => $child_term->term_id, // Keep the ID for any other use
					);
				}
				// Store parent and children in the final array
				$categories_with_children[] = array(
					'parent' => $parent_term,
					'children' => $children_with_details // Store children with their links and images
				);
			}
            
        }
        
        return $categories_with_children; 
    }
    
    add_filter( 'get_categories_by_parent', 'sc_get_categories_by_parent');
}



//widget data is an array with all widget settings
if ( ! function_exists( 'sc_show_document_taxonomy' ) ) {
    function sc_show_document_taxonomy($args, $widgetData){
        
    	$args_post = array(
    	    'post_type' => 'document',
    	    'tax_query' => array(
    	        'relation' => 'AND',
    	        array(
    	            'taxonomy' => 'software-version',
    	            'field'    => 'term_id',
    	            'terms'    => $args['parent'],
    	        ),
    	        array(
    	            'taxonomy' => 'document-category',
    	            'field'    => 'term_id',
    	            'operator' => 'EXISTS',
    	        ),
    	    ),
    	    'posts_per_page' => -1,
    	);
    	
    	unset($args['parent']);
    	
    	$posts = get_posts($args_post);
    	
    	$filtered_terms = array();
    	
    	if (!empty($posts)) {
    	    foreach ($posts as $post) {
    	        $document_category_terms = wp_get_post_terms($post->ID, 'document-category');
    	        // Process the terms as needed
    	        foreach ($document_category_terms as $term) {
    	            $filtered_terms[] = $term->term_id;
    	        }
    	    }
    	}
    	
        if (!empty($filtered_terms)) {
            $args['include'] = $filtered_terms;
        }
    	
    	return($args);
    }
    
    add_filter("show_document_taxonomy", "sc_show_document_taxonomy",10,2);
}
// Handle Ajax login request
function um_ajax_login_handle_submit() {
	if (!isset($_POST['action']) || $_POST['action'] !== 'um_ajax_login') {
		wp_send_json_error('Invalid request');
	}
	
	$form_id = isset( $_POST['form_id'] ) ? $_POST['form_id'] : 0;
	$_POST['username'] = isset( $_POST['username-' . $form_id] ) ? $_POST['username-' . $form_id] : '';
	$_POST['user_password'] = isset( $_POST['user_password-' . $form_id] ) ? $_POST['user_password-' . $form_id] : '';
	// $_POST['rememberme'] = isset( $_POST['user_password-' . $form_id] ) ? $_POST['user_password-' . $form_id] : '';
	
	
	// Process the login
	UM()->form()->post_form = wp_unslash( $_POST );
	
	do_action('um_submit_form_errors_hook_login', UM()->form()->post_form);
	
	// Check for errors
	$errors = UM()->form()->errors;
	
	if (!empty($errors)) {
		// Return first error message
		$error_message = $errors;
		wp_send_json_error(array('message' => $error_message));
	} else {
		$user_id = isset( UM()->login()->auth_id ) ? UM()->login()->auth_id : '';
		um_fetch_user( $user_id );
		UM()->user()->auto_login( um_user( 'ID' ), 1 );
		
		$redirect_to = home_url('/my-solidcam');
		
		wp_send_json_success(array(
			'message' => __('Login successful', 'ultimate-member'),
			'redirect' => $redirect_to
		));
	}	
	die();
}
add_action('wp_ajax_nopriv_um_ajax_login', 'um_ajax_login_handle_submit');

// Handle Ajax reset password request
function um_ajax_reset_password_handle_submit() {
	if (!isset($_POST['action']) || $_POST['action'] !== 'um_ajax_reset_password') {
		wp_send_json_error('Invalid request');
	}
	
	$form_id = isset($_POST['form_id']) ? $_POST['form_id'] : 0;
	// $_POST['username_b'] = isset($_POST['username_b']) ? $_POST['username_b'] : '';
	
	// Process the reset password form
	UM()->form()->post_form = wp_unslash($_POST);
	
	if (isset($_POST['_um_password_reset']) && $_POST['_um_password_reset'] == 1) {
		do_action('um_reset_password_errors_hook', UM()->form()->post_form);
		
		// Check for errors
		$errors = UM()->form()->errors;
		
		if (!empty($errors)) {
			wp_send_json_error(array('message' => $errors));
		} else {
			// Process reset password
			do_action('um_reset_password_process_hook', UM()->form()->post_form);
			
			$message = UM()->options()->get('password_reset_message');
			if (!$message) {
				$message = __('Your password has been reset successfully! An email with instructions has been sent to your registered email address.', 'ultimate-member');
			}
			
			wp_send_json_success(array(
				'message' => $message,
				'redirect' => um_get_core_page('login')
			));
		}
	} else {
		wp_send_json_error(array('message' => __('Invalid reset password request', 'ultimate-member')));
	}
	
	die();
}
// add_action('wp_ajax_nopriv_um_ajax_reset_password', 'um_ajax_reset_password_handle_submit');

/**
 * Handle webhook request for module subscription
 */
add_action('wp_ajax_send_webhook_request', 'handle_webhook_request');
function handle_webhook_request() {
	// Verify user is logged in
	if (!is_user_logged_in()) {
		wp_send_json_error(['message' => 'User not logged in']);
		return;
	}

	// Verify nonce
	check_ajax_referer('mss_addon_nonce', 'security');

	// Get module key from request
	$module_key = isset($_POST['module_key']) ? sanitize_text_field($_POST['module_key']) : '';
	if (empty($module_key)) {
		wp_send_json_error(['message' => 'Invalid module key']);
		return;
	}

	// Get active license
	$user_id = get_current_user_id();
	$active_license = get_user_meta($user_id, 'active_user_license', true);
	if (empty($active_license)) {
		wp_send_json_error(['message' => 'No active license found']);
		return;
	}

	// Prepare webhook data
	$webhook_data = [
		'lic' => $active_license,
		'module' => $module_key,
		'ip' => get_client_ip(),
		'user_agent' => $_SERVER['HTTP_USER_AGENT']
	];
	// Send webhook request
	$args = [
		'body' => $webhook_data
	];
	
	$response = wp_safe_remote_post( 'https://solid.my.salesforce-sites.com/api/services/apexrest/ElementorWebhook', $args );

	// Handle response
	if (is_wp_error($response)) {
		wp_send_json_error(['message' => 'Failed to connect to webhook: ' . $response->get_error_message()]);
		return;
	}

	$response_code = wp_remote_retrieve_response_code($response);
	if ($response_code !== 200) {
		wp_send_json_error(['message' => 'Webhook request failed with status: ' . $response_code]);
		return;
	}

	wp_send_json_success(['message' => 'Request processed successfully']);
}

/**
 * Get client IP address
 */
function get_client_ip() {
	$ip_headers = [
		'HTTP_CLIENT_IP',
		'HTTP_X_FORWARDED_FOR',
		'HTTP_X_FORWARDED',
		'HTTP_X_CLUSTER_CLIENT_IP',
		'HTTP_FORWARDED_FOR',
		'HTTP_FORWARDED',
		'REMOTE_ADDR'
	];

	foreach ($ip_headers as $header) {
		if (!empty($_SERVER[$header])) {
			$ip_array = array_map('trim', explode(',', $_SERVER[$header]));
			return $ip_array[0];
		}
	}

	return '127.0.0.1';
}

add_action( 'init', 'remove_account_page_restrict_action' );

function remove_account_page_restrict_action() {
	if ( class_exists( '\um\core\Account' ) ) {
		
		if ( UM()->access() ) {
			remove_action( 'um_access_check_global_settings', array( UM()->access(), 'um_access_check_global_settings' ) );
		}
	}
}

add_action( 'template_redirect', function() {
	if ( is_page( 'user' ) ) {
		wp_redirect( home_url( '/my-solidcam' ), 301 );
		exit;
	}
});


add_filter('pre_get_posts', 'modify_elementor_wpml_search_query');

function modify_elementor_wpml_search_query($query) {
	// Only run on frontend search and not in admin
	if (!is_admin() && $query->is_main_query() && $query->is_search()) {
		global $sitepress;
		
		// Get current language
		$current_lang = apply_filters('wpml_current_language', NULL);
		
		// Get translatable post types including custom ones
		$translatable_post_types = array_keys($sitepress->get_translatable_documents());
		
		// Get all public post types
		$post_types = get_post_types([
			'public' => true,
			'exclude_from_search' => false
		]);
		
		// Filter post types that are both public and translatable
		$search_post_types = array_intersect($post_types, $translatable_post_types);
		
		// Set query parameters
		$query->set('post_type', $search_post_types);
		$query->set('suppress_filters', false);
		$query->set('lang', $current_lang);
		
		// Handle taxonomies if present
		if ($query->get('tax_query')) {
			$tax_queries = $query->get('tax_query');
			foreach ($tax_queries as &$tax_query) {
				if (isset($tax_query['taxonomy'])) {
					$tax_query['suppress_filters'] = false;
				}
			}
			$query->set('tax_query', $tax_queries);
		}
		
		// Optional: Modify search to include custom fields
		$search_term = $query->get('s');
		if ($search_term) {
			add_filter('posts_search', function($search, $wp_query) use ($search_term) {
				global $wpdb;
				
				if (!empty($search) && !empty($wp_query->query_vars['search_terms'])) {
					$search = '';
					$n = !empty($wp_query->query_vars['exact']) ? '' : '%';
					$searchand = '';
					
					foreach ((array)$wp_query->query_vars['search_terms'] as $term) {
						$term = esc_sql($wpdb->esc_like($term));
						$search .= "{$searchand}(
							{$wpdb->posts}.post_title LIKE '{$n}{$term}{$n}'
							OR {$wpdb->posts}.post_content LIKE '{$n}{$term}{$n}'
							OR {$wpdb->posts}.post_excerpt LIKE '{$n}{$term}{$n}'
							OR EXISTS (
								SELECT * FROM {$wpdb->postmeta}
								WHERE post_id = {$wpdb->posts}.ID
								AND meta_value LIKE '{$n}{$term}{$n}'
							)
						)";
						$searchand = ' AND ';
					}
					
					if (!empty($search)) {
						$search = " AND ({$search}) ";
						if (!is_user_logged_in())
							$search .= " AND ({$wpdb->posts}.post_password = '') ";
					}
				}
				return $search;
			}, 10, 2);
		}
	}
	return $query;
}

// Modify Elementor search widget results
add_filter('elementor/widget/render_content', function($content, $widget) {
	if ('search' === $widget->get_name()) {
		$current_lang = apply_filters('wpml_current_language', NULL);
		
		// Add language parameter to search form
		$content = preg_replace(
			'/<form([^>]*)action="([^"]*)"/',
			'<form$1action="$2" data-lang="' . esc_attr($current_lang) . '"',
			$content
		);
		
		// Add hidden language input
		$content = preg_replace(
			'/<\/form>/',
			'<input type="hidden" name="lang" value="' . esc_attr($current_lang) . '"></form>',
			$content
		);
	}
	
	return $content;
}, 10, 2);


// Add Meta Box for URL
function add_url_meta_box() {
	add_meta_box(
		'success_story_url',
		'Success Story URL',
		'render_url_meta_box',
		'success-story',
		'normal',
		'high'
	);
}
add_action('add_meta_boxes', 'add_url_meta_box');

// Render Meta Box HTML
function render_url_meta_box($post) {
	wp_nonce_field('success_story_url_nonce', 'success_story_url_nonce');
	$url = get_post_meta($post->ID, '_success_story_url', true);
	?>
	<div>
		<label for="success_story_url">URL:</label>
		<input type="url" id="success_story_urls" name="success_story_url" value="<?php echo esc_attr($url); ?>" style="width: 100%;">
		<button type="button" id="scrape_url" class="button button-secondary" style="margin-top: 10px;">Scrape URL Data</button>
		<div id="scrape_status"></div>
	</div>
	<script>
		jQuery(document).ready(function($) {
			$('#scrape_url').on('click', function() {
				const url = $('#success_story_urls').val();
				if (!url) {
					alert('Please enter a URL first');
					return;
				}
				
				$(this).prop('disabled', true);
				$('#scrape_status').html('Scraping data...');
				
				$.ajax({
					url: ajaxurl,
					type: 'POST',
					data: {
						action: 'scrape_url_data',
						url: url,
						post_id: <?php echo $post->ID; ?>,
						nonce: $('#success_story_url_nonce').val()
					},
					success: function(response) {
						if (response.success) {
							$('#scrape_status').html('Data scraped successfully!');
							location.reload();
						} else {
							$('#scrape_status').html('Error: ' + response.data);
						}
					},
					error: function() {
						$('#scrape_status').html('Error occurred while scraping');
					},
					complete: function() {
						$('#scrape_url').prop('disabled', false);
					}
				});
			});
		});
	</script>
	<?php
}

// Save Meta Box Data
function save_url_meta_box($post_id) {
	if (!isset($_POST['success_story_url_nonce']) || 
		!wp_verify_nonce($_POST['success_story_url_nonce'], 'success_story_url_nonce')) {
		return;
	}
	
	if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
		return;
	}
	
	if (isset($_POST['success_story_url'])) {
		update_post_meta($post_id, '_success_story_url', sanitize_url($_POST['success_story_url']));
	}
}
add_action('save_post_success-story', 'save_url_meta_box');

// Handle AJAX URL Scraping
function handle_url_scraping() {
	check_ajax_referer('success_story_url_nonce', 'nonce');
	
	if (!isset($_POST['url']) || !isset($_POST['post_id'])) {
		wp_send_json_error('Missing required parameters');
	}
	
	$url = sanitize_url($_POST['url']);
	$post_id = intval($_POST['post_id']);
	
	// Initialize cURL
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	$html = curl_exec($ch);
	curl_close($ch);
	
	if (!$html) {
		wp_send_json_error('Failed to fetch URL');
	}
	
	// Load HTML into DOMDocument
	$doc = new DOMDocument();
	@$doc->loadHTML($html, LIBXML_NOERROR);
	$xpath = new DOMXPath($doc);
	
	// Extract images
	$images = [];
	$elements = $xpath->query("//div[contains(@class, 'ratio-21x9')]//img");
	
	
	foreach ($elements as $element) {
		$images[] = 'https://www.solidcam.com' . $element->getAttribute('src');
	}
	
	if (empty($images)) {
		wp_send_json_error('No images found');
	}
	
	// Set featured image
	$featured_image_url = array_shift($images);
	$upload_dir = wp_upload_dir();
	$featured_image_id = upload_external_image($featured_image_url, $post_id);
	if ($featured_image_id) {
		if ( function_exists( 'wp_rml_move' ) ) {
			wp_rml_move( 20, array( $featured_image_id ) );
		}
		set_post_thumbnail($post_id, $featured_image_id);
	}
	
	// Add remaining images to ACF gallery
	$gallery_images = [];
	foreach ($images as $image_url) {
		$image_id = upload_external_image($image_url, $post_id);
		if ($image_id) {
			if ( function_exists( 'wp_rml_move' ) ) {
				wp_rml_move( 20, array( $image_id ) );
			}
			$gallery_images[] = $image_id;
		}
	}
	if (!empty($gallery_images)) {
		update_field('success_story_images', $gallery_images, $post_id);
	}
	
	// Extract and set tags
	$tags = [];
	$elements = $xpath->query("//span[contains(@class, 'badge') and contains(@class, 'rounded-pill')]");
	foreach ($elements as $element) {
		$tags[] = sanitize_text_field($element->nodeValue);
	}
	
	if (!empty($tags)) {
		// Remove existing tags
		wp_delete_object_term_relationships($post_id, 'ss-tag');
		
		// Add new tags
		foreach ($tags as $tag) {
			wp_set_object_terms($post_id, $tag, 'ss-tag', true);
		}
	}
	
	wp_send_json_success();
}
add_action('wp_ajax_scrape_url_data', 'handle_url_scraping');

// Helper function to upload external image
function upload_external_image($image_url, $post_id) {
	require_once(ABSPATH . 'wp-admin/includes/media.php');
	require_once(ABSPATH . 'wp-admin/includes/file.php');
	require_once(ABSPATH . 'wp-admin/includes/image.php');
	
	// Download image
	$tmp = download_url($image_url);
	if (is_wp_error($tmp)) {
		return false;
	}
	
	$file_array = array(
		'name' => basename($image_url),
		'tmp_name' => $tmp
	);
	
	// Upload image
	$image_id = media_handle_sideload($file_array, $post_id);
	
	// Clean up
	@unlink($tmp);
	
	return is_wp_error($image_id) ? false : $image_id;
}

function filter_cpt_by_language_in_admin() {
	$current_language = apply_filters('wpml_current_language', NULL);
	
	if ($current_language !== 'de') {
		remove_menu_page( 'edit.php?post_type=sc_training' );
		remove_menu_page( 'edit.php?post_type=sc_office' );
	}
}
add_action( 'admin_menu', 'filter_cpt_by_language_in_admin' );


// Function to Get Label

function get_acf_field_choices($field_key) {
    $field = get_field_object($field_key);
    return $field ? $field['choices'] : [];
}

add_filter('get_acf_field_choices', function($field_key) {
    return get_acf_field_choices($field_key);
}, 10, 1);

function custom_logout_redirect() {
	// Check if the current request is for "/logout/"
	if (untrailingslashit($_SERVER['REQUEST_URI']) === '/logout') {
		
		// Check if the user is logged in
		if (is_user_logged_in()) {
			wp_logout(); // Log out the user
		}

		// Redirect to home page
		wp_redirect(home_url());
		exit;
	}
}
add_action('template_redirect', 'custom_logout_redirect');

/**
 * Dynamic WordPress Admin Filters & Columns
 * 
 * Adds dropdown filters and columns for taxonomies and custom fields
 * in the WordPress admin panel for specified post types.
 */

function get_post_type_taxonomy_config() {
	return [
		'document' => [
			'document-category' => 'Document Categories',
			'software-version' => 'Software Versions'
		],
		'sc_office' => [
			'region' => 'Regions'
		],
		'sc_training' => [
			'sc_software' => 'Software',
			'trainingcategory' => 'Training Categories'
		]
	];
}

/**
 * Configuration array for post types and their associated custom fields
 * Format: 'post_type' => ['field1' => ['label' => 'Label', 'type' => 'date|text|select'], ...]
 */
function get_post_type_custom_field_config() {
	return [
		'sc_training' => [
			'start_date_and_time' => [
				'label' => 'Start Date & Time',
				'type' => 'date'
			]
		]
	];
}

/**
 * Add dropdown filters for configured taxonomies
 */
function add_taxonomy_filters() {
	global $typenow;
	$config = get_post_type_taxonomy_config();
	
	// Check if current post type is in our configuration
	if (!isset($config[$typenow])) {
		return;
	}
	
	// Loop through all taxonomies configured for this post type
	foreach ($config[$typenow] as $taxonomy => $label) {
		if (taxonomy_exists($taxonomy)) {
			$selected = isset($_GET[$taxonomy]) ? $_GET[$taxonomy] : '';
			$info_taxonomy = get_taxonomy($taxonomy);
			wp_dropdown_categories([
				'show_option_all' => sprintf(__('All %s', 'textdomain'), $info_taxonomy->label),
				'taxonomy' => $taxonomy,
				'name' => $taxonomy,
				'orderby' => 'name',
				'selected' => $selected,
				'hierarchical' => true,
				'depth' => 3,
				'show_count' => true,
				'hide_empty' => true,
			]);
		}
	}
	
	// Add custom field filters
	add_custom_field_filters();
}
add_action('restrict_manage_posts', 'add_taxonomy_filters');

/**
 * Add filters for custom fields
 */
function add_custom_field_filters() {
	global $typenow;
	$custom_field_config = get_post_type_custom_field_config();
	
	// Check if current post type has custom field filters
	if (!isset($custom_field_config[$typenow])) {
		return;
	}
	
	// Loop through all custom fields for this post type
	foreach ($custom_field_config[$typenow] as $field_name => $field_config) {
		switch ($field_config['type']) {
			case 'date':
				// For date type fields, add a date filter
				$current_value = isset($_GET[$field_name]) ? $_GET[$field_name] : '';
				?>
				<input 
					type="date" 
					name="<?php echo esc_attr($field_name); ?>" 
					placeholder="<?php echo esc_attr($field_config['label']); ?>"
					value="<?php echo esc_attr($current_value); ?>"
					class="date-picker"
				>
				<?php
				break;
				
			case 'select':
				// For select type fields, query for unique values and create a dropdown
				// Implementation depends on your specific needs
				break;
				
			case 'text':
				// For text fields, add a simple text input
				$current_value = isset($_GET[$field_name]) ? $_GET[$field_name] : '';
				?>
				<input 
					type="text" 
					name="<?php echo esc_attr($field_name); ?>" 
					placeholder="<?php echo esc_attr($field_config['label']); ?>"
					value="<?php echo esc_attr($current_value); ?>"
				>
				<?php
				break;
		}
	}
	
	// Add a bit of styling for the date picker
	?>
	<style>
		.date-picker {
			height: 28px;
			margin: 0 4px;
			vertical-align: middle;
		}
	</style>
	<?php
}

/**
 * Make the taxonomy filters work by converting term ID to slug
 */
function convert_taxonomy_term_in_query($query) {
	global $pagenow, $typenow;
	$config = get_post_type_taxonomy_config();
	
	// Only process on edit.php page for configured post types
	if ($pagenow != 'edit.php') {
		return;
	}
	
	// Check if post type is set and is a string
	if (!isset($query->query['post_type']) || !is_string($query->query['post_type'])) {
		return;
	}
	
	// Check if the post type exists in our configuration
	if (!array_key_exists($query->query['post_type'], $config)) {
		return;
	}
	
	// Loop through all taxonomies for this post type
	foreach (array_keys($config[$query->query['post_type']]) as $taxonomy) {
		// Convert term IDs to slugs
		if (isset($_GET[$taxonomy]) && is_numeric($_GET[$taxonomy]) && $_GET[$taxonomy] != 0) {
			$term = get_term_by('id', $_GET[$taxonomy], $taxonomy);
			if ($term) {
				$query->query_vars[$taxonomy] = $term->slug;
			}
		}
	}
	
	// Apply custom field filters to query
	apply_custom_field_filters($query);
}
add_filter('parse_query', 'convert_taxonomy_term_in_query');

/**
 * Apply custom field filters to WP_Query
 */
function apply_custom_field_filters($query) {
	global $typenow;
	$custom_field_config = get_post_type_custom_field_config();
	
	// Check if current post type has custom field filters
	if (!isset($custom_field_config[$typenow])) {
		return;
	}
	
	// Get existing meta query if any
	$meta_query = $query->get('meta_query');
	if (!is_array($meta_query)) {
		$meta_query = [];
	}
	
	// Loop through all custom fields for this post type
	foreach ($custom_field_config[$typenow] as $field_name => $field_config) {
		if (!empty($_GET[$field_name])) {
			switch ($field_config['type']) {
				case 'date':
					// For date fields, filter by the date (start of day)
					$date_value = sanitize_text_field($_GET[$field_name]);
					
					// Check if this is a valid date
					if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_value)) {
						// Get start and end of the selected day
						$start_date = $date_value . ' 00:00:00';
						$end_date = $date_value . ' 23:59:59';
						
						// Query for posts with start_date_and_time on the selected day
						$meta_query[] = [
							'key' => $field_name,
							'value' => [$start_date, $end_date],
							'compare' => 'BETWEEN',
							'type' => 'DATETIME'
						];
					}
					break;
					
				default:
					// For other fields, do a simple equality check
					$meta_query[] = [
						'key' => $field_name,
						'value' => sanitize_text_field($_GET[$field_name]),
					];
					break;
			}
		}
	}
	
	// Apply the meta query if we added any conditions
	if (count($meta_query) > 0) {
		$query->set('meta_query', $meta_query);
	}
}

/**
 * Add custom columns to the admin pages for configured post types
 */
function add_custom_taxonomy_columns($columns) {
	global $typenow;
	$taxonomy_config = get_post_type_taxonomy_config();
	$custom_field_config = get_post_type_custom_field_config();
	
	// Create new columns array
	$new_columns = [];
	
	// Insert columns after title or other appropriate position
	foreach ($columns as $key => $value) {
		$new_columns[$key] = $value;
		
		if ($key === 'title') {
			// Add taxonomy columns for this post type
			if (isset($taxonomy_config[$typenow])) {
				foreach ($taxonomy_config[$typenow] as $taxonomy => $label) {
					$column_key = sanitize_key($taxonomy) . '_column';
					$new_columns[$column_key] = __($label, 'textdomain');
				}
			}
			
			// Add custom field columns for this post type
			if (isset($custom_field_config[$typenow])) {
				foreach ($custom_field_config[$typenow] as $field_name => $field_config) {
					$column_key = sanitize_key($field_name) . '_column';
					$new_columns[$column_key] = __($field_config['label'], 'textdomain');
				}
			}
		}
	}
	
	return $new_columns;
}

/**
 * Apply the custom columns filter for each configured post type
 */
function register_custom_column_filters() {
	$taxonomy_config = get_post_type_taxonomy_config();
	$custom_field_config = get_post_type_custom_field_config();
	
	// Combine all post types from both configs
	$post_types = array_unique(array_merge(
		array_keys($taxonomy_config),
		array_keys($custom_field_config)
	));
	
	foreach ($post_types as $post_type) {
		add_filter("manage_{$post_type}_posts_columns", 'add_custom_taxonomy_columns');
		add_action("manage_{$post_type}_posts_custom_column", 'display_custom_columns', 10, 2);
		add_filter("manage_edit-{$post_type}_sortable_columns", 'make_custom_columns_sortable');
	}
}
add_action('admin_init', 'register_custom_column_filters');

/**
 * Display taxonomy terms and custom field values in the columns
 */
function display_custom_columns($column_name, $post_id) {
	global $typenow;
	$taxonomy_config = get_post_type_taxonomy_config();
	$custom_field_config = get_post_type_custom_field_config();
	
	// Display taxonomy terms
	if (isset($taxonomy_config[$typenow])) {
		foreach ($taxonomy_config[$typenow] as $taxonomy => $label) {
			$column_key = sanitize_key($taxonomy) . '_column';
			
			if ($column_name === $column_key) {
				$terms = get_the_terms($post_id, $taxonomy);
				if (!empty($terms) && !is_wp_error($terms)) {
					$term_links = [];
					foreach ($terms as $term) {
						$term_links[] = sprintf(
							'<a href="%s">%s</a>',
							esc_url(add_query_arg(['post_type' => $typenow, $taxonomy => $term->slug], 'edit.php')),
							esc_html($term->name)
						);
					}
					echo implode(', ', $term_links);
				} else {
					echo '<span class="na">–</span>';
				}
				return; // Found and displayed, so exit
			}
		}
	}
	
	// Display custom field values
	if (isset($custom_field_config[$typenow])) {
		foreach ($custom_field_config[$typenow] as $field_name => $field_config) {
			$column_key = sanitize_key($field_name) . '_column';
			
			if ($column_name === $column_key) {
				$field_value = get_post_meta($post_id, $field_name, true);
				
				if (!empty($field_value)) {
					switch ($field_config['type']) {
						case 'date':
							// Format date for display
							$date_obj = DateTime::createFromFormat('Y-m-d H:i:s', $field_value);
							if ($date_obj) {
								echo esc_html($date_obj->format('F j, Y, g:i a'));
							} else {
								// Try to handle just date without time
								$date_obj = DateTime::createFromFormat('Y-m-d', $field_value);
								if ($date_obj) {
									echo esc_html($date_obj->format('F j, Y'));
								} else {
									// Just output as is
									echo esc_html($field_value);
								}
							}
							break;
							
						default:
							echo esc_html($field_value);
							break;
					}
				} else {
					echo '<span class="na">–</span>';
				}
				return; // Found and displayed, so exit
			}
		}
	}
}

/**
 * Make the custom columns sortable
 */
function make_custom_columns_sortable($columns) {
	global $typenow;
	$taxonomy_config = get_post_type_taxonomy_config();
	$custom_field_config = get_post_type_custom_field_config();
	
	// Make taxonomy columns sortable
	if (isset($taxonomy_config[$typenow])) {
		foreach ($taxonomy_config[$typenow] as $taxonomy => $label) {
			$column_key = sanitize_key($taxonomy) . '_column';
			$columns[$column_key] = $column_key;
		}
	}
	
	// Make custom field columns sortable
	if (isset($custom_field_config[$typenow])) {
		foreach ($custom_field_config[$typenow] as $field_name => $field_config) {
			$column_key = sanitize_key($field_name) . '_column';
			$columns[$column_key] = $field_name;
		}
	}
	
	return $columns;
}

/**
 * Handle sorting for taxonomy and custom field columns
 */
function custom_columns_orderby($query) {
	if (!is_admin() || !$query->is_main_query()) {
		return;
	}
	
	global $typenow;
	$orderby = $query->get('orderby');
	
	// Handle sorting by custom field
	$custom_field_config = get_post_type_custom_field_config();
	if (isset($custom_field_config[$typenow])) {
		foreach ($custom_field_config[$typenow] as $field_name => $field_config) {
			if ($orderby === $field_name) {
				$query->set('meta_key', $field_name);
				
				// Set appropriate orderby based on field type
				switch ($field_config['type']) {
					case 'date':
						$query->set('orderby', 'meta_value');
						break;
					default:
						$query->set('orderby', 'meta_value');
						break;
				}
			}
		}
	}
	
	// Handle sorting by taxonomy (for column keys matching "{taxonomy}_column")
	$taxonomy_config = get_post_type_taxonomy_config();
	if (isset($taxonomy_config[$typenow])) {
		foreach ($taxonomy_config[$typenow] as $taxonomy => $label) {
			$column_key = sanitize_key($taxonomy) . '_column';
			
			if ($orderby === $column_key) {
				// Set query to order by taxonomy name
				$query->set('orderby', 'tax_' . $taxonomy);
				$query->set('tax_query', [
					[
						'taxonomy' => $taxonomy,
						'field' => 'term_id',
						'terms' => get_terms([
							'taxonomy' => $taxonomy,
							'fields' => 'ids',
						]),
					],
				]);
			}
		}
	}
}
add_action('pre_get_posts', 'custom_columns_orderby');

### Jack Hanington Edit 7/30/2025 to fix 404 pages not working https://support.rankmath.com/ticket/error-404-not-being-monitored/
add_filter( 'rank_math/404_monitor/hook', function($hook){
	return 'template_redirect'; 
});

function get_documentation_user_permission() {
	$permission = 'false';

	if ( is_user_logged_in() ) {
		$current_user  = wp_get_current_user();
		$allowed_roles = array( 'administrator', 'reseller', 'partner', 'staff', 'customer' );

		if ( ! empty( $current_user->roles ) && is_array( $current_user->roles ) ) {
			if ( array_intersect( $allowed_roles, $current_user->roles ) ) {
				$permission = 'true';
			}
		}
	}
	return $permission;
}
add_filter( 'get_documentation_user_permission', 'get_documentation_user_permission', 10, 1 );

function solidcam_str_translate( $string ){
	return __( $string, 'solidcam' );
}
add_filter( 'solidcam_str_translate', 'solidcam_str_translate');

// Download Shortcode einbinden
require_once get_stylesheet_directory() . '/includes/download-shortcode.php';

function shortcode_acf_tablefield( $atts ) {

    $param = shortcode_atts( array(
        'field-name' => false,
        'subfield-name' => false,
        'post-id' => false,
        'table-class' => '',
    ), $atts );

    $class = new class( $param ) {

        public $atts;

        public $field_names;

        public $field_data = '';

        public $html = '';

        public function __construct( $atts ) {

            if ( is_string( $atts['subfield-name'] ) ) {

                $atts['field-name'] = $atts['subfield-name'];
            }

            if ( ! is_string( $atts['field-name'] ) ) {

                return '';
            }

            $this->atts = $atts;

            $this->field_names = explode( '/', $this->atts['field-name'] );

            $this->subfield( 0 );

        }

        private function may_get_table_html( $data ) {

            if ( isset( $data['body'] ) ) {

                $return = '<table class="' . $this->atts['table-class'] . '">';

                    if ( ! empty( $data['caption'] ) ) {

                        echo '<caption>' . $data['caption'] . '</caption>';
                    }

                    if ( $data['header'] ) {

                        $return .= '<thead>';

                            $return .= '<tr>';

                                foreach ( $data['header'] as $th ) {

                                    $return .= '<th>';
                                        $return .= $th['c'];
                                    $return .= '</th>';
                                }

                            $return .= '</tr>';

                        $return .= '</thead>';
                    }

                    $return .= '<tbody>';

                        foreach ( $data['body'] as $tr ) {

                            $return .= '<tr>';

                                foreach ( $tr as $td ) {

                                    $return .= '<td>';
                                        $return .= $td['c'];
                                    $return .= '</td>';
                                }

                            $return .= '</tr>';
                        }

                    $return .= '</tbody>';

                $return .= '</table>';

                $this->html .= $return;
            }
        }

        private function subfield( $level = 0, $data = null ) {

            if ( isset( $data['body'] ) ) {

                $this->may_get_table_html( $data );

                return;
            }
            else if ( ! isset( $this->field_names[ $level ] ) ) {

                return;
            }
            else if ( $data === null ) {

                if ( $this->atts['subfield-name'] === false ) {

                    $data = get_field(  $this->field_names[0],  $this->atts['post-id'] );
                }
                else {

                    $data = get_sub_field( $this->field_names[0] );
                }
            }
            else if ( isset( $data[ $this->field_names[ $level ] ] ) ) {

                $data = $data[ $this->field_names[ $level ] ];
            }

            // repeater/group field
            if (
                is_array( $data ) &&
                isset( $data[0] ) &&
                ! isset( $data[0]['acf_fc_layout'] )
                ) {

                if ( is_numeric( $this->field_names[ $level + 1 ] )  ) {

                    if ( isset( $data[  $this->field_names[ $level + 1 ] ] ) ) {

                        $this->subfield( $level + 1, $data[  $this->field_names[ $level + 1 ] ] );
                    }
                }
                else {

                    foreach( $data as $key => $item ) {

                        $this->subfield( $level + 1, $item );
                    }
                }
            }

            // flexible content field
            else if (
                is_array( $data ) &&
                isset( $data[0] ) &&
                isset( $data[0]['acf_fc_layout'] )
                ) {

                foreach( $data as $key => $item ) {

                    if (
                        $item['acf_fc_layout'] === $this->field_names[ $level + 1 ] &&
                        isset( $item[ $this->field_names[ $level + 2 ] ] )
                    ) {

                        $this->subfield( $level + 2, $item[ $this->field_names[ $level + 2 ] ] );
                    }
                }
            }

            // table field
            else {

                $this->subfield( $level + 1, $data );
            }
        }
    };

    return $class->html;
}

add_shortcode( 'tablefield', 'shortcode_acf_tablefield' );