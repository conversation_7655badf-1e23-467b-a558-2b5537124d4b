<?php
/**
 * Modern Download Shortcode for ACF Repeater
 * 
 * @package SolidCAM
 * @version 1.1
 */

// Sicherheit - Direktzugriff verhindern
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Hauptklasse für den Download Shortcode
 */
class SolidCAM_Download_Shortcode {
    
    /**
     * Konstruktor - Hooks registrieren
     */
    public function __construct() {
        add_shortcode('dl', [$this, 'render_download_shortcode']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_styles']);
    }
    
    /**
     * CSS einbinden
     */
    public function enqueue_styles() {
        wp_enqueue_style(
            'solidcam-download-shortcode',
            get_stylesheet_directory_uri() . '/assets/css/download-shortcode.css',
            [],
            time() // Cache-Busting während Entwicklung
        );
    }
    
    /**
     * Hauptversion des Download Shortcodes
     */
    public function render_download_shortcode($atts) {
        $file = get_sub_field('sc_video_related_file');
        $custom_name = get_sub_field('sc_video_related_filename');
        
        if (!$file) return '';
        
        // Hole Datei-Infos
        $file_id = is_array($file) ? $file['ID'] : $file;
        $url = is_array($file) ? $file['url'] : wp_get_attachment_url($file_id);
        $file_path = get_attached_file($file_id);
        
        // Original Dateiname (ohne Erweiterung)
        $original_filename = pathinfo(basename($url), PATHINFO_FILENAME);
        
        // Display Name: Custom Name oder "Download Part" als Fallback
        $display_name = !empty($custom_name) ? $custom_name : 'Download Part';
        
        // Dateigröße formatieren
        $file_size = $this->format_filesize($file_path);
        
        // Dateityp
        $file_extension = strtoupper(pathinfo($url, PATHINFO_EXTENSION));
        
        // HTML ausgeben OHNE Inline-Styles
        ob_start();
        ?>
        <a href="<?php echo esc_url($url); ?>" download class="solidcam-dl-item">
            <div class="dl-icon-wrap">
                <svg class="dl-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
                    <path d="M352 96L352 64L288 64L288 306.7C257.3 276 236 254.7 224 242.7L178.7 288C181.6 290.9 221.1 330.4 297.3 406.6L320 429.3C322.9 426.4 362.4 386.9 438.6 310.7L461.3 288L416 242.7C404 254.7 382.7 276 352 306.7L352 96zM96 384L96 544L544 544L544 384L433.1 384C395.4 421.7 357.7 459.4 320 497.1C282.3 459.4 244.6 421.7 206.9 384L96 384zM464 440C477.3 440 488 450.7 488 464C488 477.3 477.3 488 464 488C450.7 488 440 477.3 440 464C440 450.7 450.7 440 464 440z"/>
                </svg>
            </div>
            <div class="dl-info">
                <div class="dl-name"><?php echo esc_html($display_name); ?></div>
                <div class="dl-meta">
                    <?php if ($use_uploaded && !empty($original_filename)) : ?>
                        <span class="filename">(<?php echo esc_html($original_filename); ?>)</span>
                        <?php if (!empty($file_extension)) : ?>
                            <span class="separator">•</span>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if (!empty($file_extension)) : ?>
                        <span class="filetype"><?php echo esc_html($file_extension); ?></span>
                    <?php endif; ?>
                    <?php if (!empty($file_size)) : ?>
                        <span class="separator">•</span>
                        <span class="filesize"><?php echo esc_html($file_size); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </a>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Dateigröße formatieren
     */
    private function format_filesize($file_path) {
        if (!$file_path || !file_exists($file_path)) {
            return '';
        }
        
        $bytes = filesize($file_path);
        
        if ($bytes < 1024) {
            return $bytes . ' B';
        } elseif ($bytes < 1048576) {
            return round($bytes / 1024, 1) . ' KB';
        } else {
            return round($bytes / 1048576, 1) . ' MB';
        }
    }
}

// Initialisieren
new SolidCAM_Download_Shortcode();