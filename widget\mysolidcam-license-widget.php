<?php
if (!defined('ABSPATH')) exit;

class Custom_License_Widget extends \Elementor\Widget_Base {
	public function get_name() {
		return 'license_management';
	}

	public function get_title() {
		return __('License Management', 'solidcam');
	}

	public function get_icon() {
		return 'eicon-lock';
	}

	public function get_categories() {
		return ['general'];
	}

	protected function validate_user_access() {
		$user_id = get_current_user_id();
		if (!$user_id) {
			echo '<p>' . esc_html__('You must be logged in to manage licenses.', 'solidcam') . '</p>';
			return false;
		}
		return $user_id;
	}

	protected function get_user_license_data($user_id) {
		return [
			'active_license' => get_user_meta($user_id, 'active_user_license', true),
			'licenses_enabled' => get_user_meta($user_id, 'user_licenses_enabled', true) ?: [],
			'dongle_no' => get_user_meta($user_id, 'dongle_no', true),
			'subscription_status' => get_user_meta($user_id, 'subscription_status', true)
		];
	}


	protected function is_license_expired($sub_end_date) {
		if (empty($sub_end_date)) return false;
		
		$expiry_date = DateTime::createFromFormat('m/d/Y', $sub_end_date);
		$current_date = new DateTime();
		
		return $expiry_date && $expiry_date < $current_date;
	}

	protected function get_software_name_from_modules($customer_modules, $license_number) {
		if( empty($customer_modules) || empty($license_number) ) {
			return 'SolidCAM';
		}
		// Module mapping based on true/false values
		$available_modules = [
			'Add-on' => 'SolidCAM Add-in',
			'CAD+CAM' => 'SolidCAM CAD/CAM Suite',
			'IVCAM' => 'InventorCAM Add-in',
			'SCMV' => 'SolidCAM Maker Version',
			'SCSE' => 'SolidCAM Add-In for Solid Edge',
			'ICMV' => 'InventorCAM Maker Version'
		];
		
		// Find the matching license in customer_modules
		$matched_array = array_filter($customer_modules, function ($sub_array) use ($license_number) {
			return isset($sub_array['license_number']) && $sub_array['license_number'] == $license_number;
		});
		
		if (empty($matched_array)) {
			return 'SolidCAM'; // Default fallback
		}
		
		$matched_license = current($matched_array);
		$enabled_modules = [];
		
		// Check which modules are enabled (true)
		foreach ($available_modules as $module_key => $module_name) {
			if (isset($matched_license[$module_key]) && $matched_license[$module_key] === 'true') {
				$enabled_modules[] = $module_name;
			}
		}
		
		// Return the first enabled module, or default to SolidCAM
		return !empty($enabled_modules) ? implode(', ', $enabled_modules) : 'SolidCAM';
	}

	protected function get_categorized_licenses($user_id) {
		$customer_all_licenses = get_user_meta($user_id, 'customer_all_licenses', true) ?: [];
		$customer_modules = get_user_meta($user_id, 'customer_modules', true) ?: [];
		$active_license = get_user_meta($user_id, 'active_user_license', true);
		
		// License data processing
		$selected = [];
		$available = [];
		$expired = [];
		
		// First, categorize all licenses
		foreach ($customer_all_licenses as $license) {
			$license_number = $license['license_number'];
			
			// Determine software name from customer_modules
			$license['software_name'] = $this->get_software_name_from_modules($customer_modules, $license_number);
			
			if ($license_number === $active_license) {
				$license['is_active'] = true;
				$selected[] = $license;
			} elseif ($this->is_license_expired($license['sub_end_date'])) {
				$license['is_active'] = false;
				$expired[] = $license;
			} else {
				$license['is_active'] = false;
				$available[] = $license;
			}
		}
		
		// If no active license exists but there are available licenses, make the first one active
		if (empty($selected) && !empty($available)) {
			$first_available = $available[0];
			$first_available['is_active'] = true;
			$selected[] = $first_available;
			
			// Remove the first available from available array
			array_shift($available);
			
			// Update user meta to set this license as active
			update_user_meta($user_id, 'active_user_license', $first_available['license_number']);
		}
		
		return compact('selected', 'available', 'expired');
	}

	protected function get_license_icon($is_active, $type) {
		if ($is_active) {
			// Green key for active license
			return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
				<path d="M400 416C497.2 416 576 337.2 576 240C576 142.8 497.2 64 400 64C302.8 64 224 142.8 224 240C224 258.7 226.9 276.8 232.3 293.7L71 455C66.5 459.5 64 465.6 64 472L64 552C64 565.3 74.7 576 88 576L168 576C181.3 576 192 565.3 192 552L192 512L232 512C245.3 512 256 501.3 256 488L256 448L296 448C302.4 448 308.5 445.5 313 441L346.3 407.7C363.2 413.1 381.3 416 400 416zM440 160C462.1 160 480 177.9 480 200C480 222.1 462.1 240 440 240C417.9 240 400 222.1 400 200C400 177.9 417.9 160 440 160z"></path>
			</svg>';
		} else if ($type === 'available') {
			// Gray lock for all other licenses
			return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
				<path d="M480 96C444.7 96 416 124.7 416 160L416 224L448 224C483.3 224 512 252.7 512 288L512 512C512 547.3 483.3 576 448 576L192 576C156.7 576 128 547.3 128 512L128 288C128 252.7 156.7 224 192 224L352 224L352 160C352 89.3 409.3 32 480 32C550.7 32 608 89.3 608 160L608 192C608 209.7 593.7 224 576 224C558.3 224 544 209.7 544 192L544 160C544 124.7 515.3 96 480 96zM360 424C373.3 424 384 413.3 384 400C384 386.7 373.3 376 360 376L280 376C266.7 376 256 386.7 256 400C256 413.3 266.7 424 280 424L360 424z"/></path>
			</svg>';
		} else {
			return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
				<path d="M320 96C355.3 96 384 124.7 384 160L384 224L256 224L256 160C256 124.7 284.7 96 320 96zM192 160L192 224C156.7 224 128 252.7 128 288L128 512C128 547.3 156.7 576 192 576L448 576C483.3 576 512 547.3 512 512L512 288C512 252.7 483.3 224 448 224L448 160C448 89.3 390.7 32 320 32C249.3 32 192 89.3 192 160zM344 360L344 440C344 453.3 333.3 464 320 464C306.7 464 296 453.3 296 440L296 360C296 346.7 306.7 336 320 336C333.3 336 344 346.7 344 360z"></path>
			</svg>';
		}
	}

	protected function get_info_icon() {
		return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
			<path d="M320 576C461.4 576 576 461.4 576 320C576 178.6 461.4 64 320 64C178.6 64 64 178.6 64 320C64 461.4 178.6 576 320 576zM280 400L304 400L304 336L256 336L256 288L352 288L352 400L384 400L384 448L256 448L256 400L280 400zM352 256L288 256L288 192L352 192L352 256z"></path>
		</svg>';
	}

	protected function get_action_button($license, $type) {
		$license_number = $license['license_number'];
		
		switch ($type) {
			case 'selected':
				// Selected button for active license
				return '<a class="elementor-button elementor-button-link elementor-size-sm" role="button">
					<span class="elementor-button-content-wrapper">
						<span class="elementor-button-icon">
							<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
								<path d="M557 152.9L538.2 178.8L282.2 530.8L260.2 561.1C259.5 560.4 208 508.9 105.7 406.6L83 384L128.3 338.7C130.2 340.6 171.6 382 252.4 462.8L486.4 141.1L505.2 115.2L557 152.8z"></path>
							</svg>
						</span>
						<span class="elementor-button-text">' . __('Selected', 'solidcam') . '</span>
					</span>
				</a>';
				
			case 'available':
				// Select button for available licenses
				return '<a class="elementor-button elementor-button-link elementor-size-sm license-select-btn" data-license="' . esc_attr($license_number) . '">
					<span class="elementor-button-content-wrapper">
						<span class="elementor-button-icon">
							<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
								<path d="M320 96C239.2 96 174.5 132.8 127.4 176.6C80.6 220.1 49.3 272 34.4 307.7C31.1 315.6 31.1 324.4 34.4 332.3C49.3 368 80.6 420 127.4 463.4C174.5 507.1 239.2 544 320 544C400.8 544 465.5 507.2 512.6 463.4C559.4 419.9 590.7 368 605.6 332.3C608.9 324.4 608.9 315.6 605.6 307.7C590.7 272 559.4 220 512.6 176.6C465.5 132.9 400.8 96 320 96zM176 320C176 240.5 240.5 176 320 176C399.5 176 464 240.5 464 320C464 399.5 399.5 464 320 464C240.5 464 176 399.5 176 320zM320 256C320 291.3 291.3 320 256 320C244.5 320 233.7 317 224.3 311.6C223.3 322.5 224.2 333.7 227.2 344.8C240.9 396 293.6 426.4 344.8 412.7C396 399 426.4 346.3 412.7 295.1C400.5 249.4 357.2 220.3 311.6 224.3C316.9 233.6 320 244.4 320 256z"></path>
							</svg>
						</span>
						<span class="elementor-button-text">' . __('Select', 'solidcam') . '</span>
					</span>
				</a>';
				
			case 'expired':
			default:
				// Expired button (non-clickable)
				return '<span class="elementor-button elementor-button-link elementor-size-sm expired-license-btn">
					<span class="elementor-button-content-wrapper">
						<span class="elementor-button-text">' . __('Expired', 'solidcam') . '</span>
					</span>
				</span>';
		}
	}



	protected function render_no_licenses_box() {
		?>
		<div class="no-licenses-box">
			<div class="icon-box">
				<i class="fa-solid fa-triangle-exclamation fa-2xl" aria-hidden="true"></i>
			</div>
			<div class="content-box">
				<h4 class="heading"><?php esc_html_e('No dongle number/software key stored', 'solidcam'); ?></h4>
				<p class="description">
					<?php esc_html_e('You can add/update a correct dongle number/software-key to manage your licenses (this is not required)', 'solidcam'); ?>
				</p>
				<a class="add-dongle-button edit-profile-btn" href="#">
					<?php esc_html_e('Add valid dongle number/software-key', 'solidcam'); ?>
					<i class="fa-regular fa-unlock-keyhole ms-2" aria-hidden="true"></i>
				</a>
			</div>
		</div>
		<?php
	}

	protected function render_pagination($section_id, $items, $per_page) {
		$total_items = count($items);
		if ($total_items <= $per_page) return; // No pagination needed
		
		$total_pages = ceil($total_items / $per_page);
		
		?>
		<div class="license-pagination" data-section="<?php echo esc_attr($section_id); ?>">
			<?php for ($page = 1; $page <= $total_pages; $page++): ?>
				<button class="pagination-btn <?php echo $page === 1 ? 'active' : ''; ?>" 
						data-page="<?php echo $page; ?>">
					<?php echo $page; ?>
				</button>
			<?php endfor; ?>
		</div>
		<?php
	}

	protected function render_license_row($license, $type, $index = 0, $per_page = 3) {
		$license_number = $license['license_number'];
		$sub_end_date = $license['sub_end_date'] ?? '';
		$software_name = $license['software_name'] ?? 'SolidCAM';

		$is_active = $license['is_active'];
		
		$icon_svg = $this->get_license_icon($is_active, $type);
		$action_button = $this->get_action_button($license, $type);
		
		// Hide items beyond first page initially
		$is_hidden = ($type !== 'selected' && $index >= $per_page);
		
		?>
		<div class="license-row <?php echo $type === 'expired' ? 'license-expired' : ''; ?>" 
			 data-type="<?php echo esc_attr($type); ?>" 
			 data-index="<?php echo $index; ?>"
			 style="<?php echo $is_hidden ? 'display: none;' : ''; ?>">
			<!-- License Icon -->
			<div class="license-icon elementor-widget elementor-widget-icon">
				<div class="elementor-widget-container">
					<div class="elementor-icon-wrapper">
						<div class="elementor-icon">
							<?php echo $icon_svg; ?>
						</div>
					</div>
				</div>
			</div>
			
			<!-- License Number -->
			<div class="license-number elementor-widget elementor-widget-heading">
				<div class="elementor-widget-container">
					<div class="elementor-heading-title elementor-size-default"><?php echo esc_html($license_number); ?></div>
				</div>
			</div>
			
			<!-- Info Icon with Tooltip -->
			<div class="license-info mysc-tooltip elementor-widget elementor-widget-icon" data-tooltip="Software: <?php echo esc_attr($software_name); ?>&#10;Valid until: <?php echo esc_attr($sub_end_date); ?>">
				<div class="elementor-widget-container">
					<div class="elementor-icon-wrapper">
						<div class="elementor-icon">
							<?php echo $this->get_info_icon(); ?>
						</div>
					</div>
				</div>
			</div>
			
			<!-- Spacer -->
			<div class="license-spacer"></div>
			
			<!-- Action Button (right aligned) -->
			<div class="license-action">
				<div class="elementor-button-wrapper">
					<?php echo $action_button; ?>
				</div>
			</div>
		</div>
		<?php
	}



	


	protected function render() {
		$user_id = $this->validate_user_access();
		if (!$user_id) return;
		
		$license_data = $this->get_user_license_data($user_id);
		$categorized = $this->get_categorized_licenses($user_id);
		$total_licenses = count($categorized['selected']) + count($categorized['available']) + count($categorized['expired']);
		
		?>
		<div class="custom-license-widget">
			<!-- Header -->
			<div class="license-header">
				<h4 class="elementor-heading-title elementor-size-default"><?php printf(esc_html__('My License (%d)', 'solidcam'), $total_licenses); ?></h4>
				<div class="license-search-container">
					<input type="text" id="license-search" class="license-search-input" placeholder="<?php esc_attr_e('Search licenses...', 'solidcam'); ?>" />
					<i class="fa fa-search license-search-icon" aria-hidden="true"></i>
					<i class="fa fa-times license-clear-icon" aria-hidden="true" style="display: none;"></i>
				</div>
			</div>
			
		<?php if ($total_licenses == 0 && (empty($license_data['dongle_no']) || $license_data['subscription_status'] == 'false')): ?>
			<?php $this->render_no_licenses_box(); ?>
		<?php endif; ?>
			
			<!-- Selected License (always on top) -->
			<?php if (!empty($categorized['selected'])): ?>
				<?php foreach ($categorized['selected'] as $index => $license): ?>
					<?php $this->render_license_row($license, 'selected', $index, 1); ?>
				<?php endforeach; ?>
			<?php endif; ?>
			
			<!-- Available Licenses -->
			<?php if (!empty($categorized['available'])): ?>
				<div class="license-section available-section">
					<?php foreach ($categorized['available'] as $index => $license): ?>
						<?php $this->render_license_row($license, 'available', $index, 10); ?>
					<?php endforeach; ?>
					<?php $this->render_pagination('available', $categorized['available'], 10); ?>
				</div>
			<?php endif; ?>
			
			<!-- Expired Licenses -->
			<?php if (!empty($categorized['expired'])): ?>
				<div class="license-section expired-section">
					<?php foreach ($categorized['expired'] as $index => $license): ?>
						<?php $this->render_license_row($license, 'expired', $index, 5); ?>
					<?php endforeach; ?>
					<?php $this->render_pagination('expired', $categorized['expired'], 5); ?>
				</div>
			<?php endif; ?>
		</div>
		<?php
	}
}