/**
* Theme Name:     Hello Elementor Child
* Author:         Elementor Team
* Template:       hello-elementor
* Text Domain:	   hello-elementor-child
* Description:    Hello Elementor is a lightweight and minimalist WordPress theme that was built specifically to work seamlessly with the Elementor site builder plugin. The theme is free, open-source, and designed for users who want a flexible, easy-to-use, and customizable website. The theme, which is optimized for performance, provides a solid foundation for users to build their own unique designs using the Elementor drag-and-drop site builder. Its simplicity and flexibility make it a great choice for both beginners and experienced Web Creators.
*/

body {
  font-family: var(--e-global-typography-primary-font-family) !important;
}

body a:focus {
  outline: none !important;
}

body .menu-item .elementor-item:before {
  transform: none;
}

body * p:last-child {
  margin-bottom: 0px !important;
}
.no-scroll {
  overflow: hidden;
}

body .um:not(.um-admin) {
  opacity: 1;
}

.link-dark {
  color: var(--e-global-color-text) !important;
}
/* body *:after,
body *:before {
	font-family: "Font Awesome 6 Sharp";
} */

/*Loader*/
.loader {
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid var( --e-global-color-b07f7d2 );
  border-bottom: 16px solid var( --e-global-color-b07f7d2 );
  width: 40px;
  height: 40px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
  display:inline-block;
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header CSS */
header {
  background-image: url(data:image/svg+xml,%3csvg\ xmlns=\'http://www.w3.org/2000/svg\'\ viewBox=\'0\ 0\ 1920\ 161\'%3e%3cg\ opacity=\'0.5\'%3e%3cpolygon\ points=\'1076\ 0\ 1920\ 161\ 1920\ 0\ 1076\ 0\'\ fill=\'%23e9ecef\'/%3e%3c/g%3e%3cg\ opacity=\'0.5\'%3e%3cpolygon\ points=\'1920\ 48\ 0\ 161\ 0\ 0\ 1920\ 0\ 1920\ 48\'\ fill=\'%23e9ecef\'/%3e%3c/g%3e%3cg\ opacity=\'0.5\'%3e%3cpolygon\ points=\'1797\ 0\ 0\ 144.92\ 0\ 0\ 1797\ 0\ 1797\ 0\'\ fill=\'%23e9ecef\'/%3e%3c/g%3e%3cpolygon\ points=\'0\ 128.13\ 0\ 0\ 1479\ 0\ 0\ 128.13\'\ fill=\'%23e9ecef\'/%3e%3c/svg%3e);
  background-repeat: no-repeat;
  background-size: 110%;
  background-position: top;
  background-color: var(--e-global-color-dc92533);
}

body .box-shadow {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/*login Popup*/
 .login-form-shortcode .um-form .um-field-label {
  color: var(--e-global-color-text);
  font-family: var(--e-global-typography-c2636a5-font-family), Sans-serif;
  font-size: var(--e-global-typography-48312dc-font-size);
  line-height: var(--e-global-typography-48312dc-line-height);
}
.login-form-shortcode .um-form .um-field {
  padding: 0;
  margin-bottom: 16px;
}
body .login-form-shortcode .um-form .um-field input {
  border-color: var(--e-global-color-text) !important;
  height: 36px !important;
  padding: 6px 12px !important;
  font-family: var(--e-global-typography-c2636a5-font-family), Sans-serif;
  font-size: var(--e-global-typography-48312dc-font-size);
  line-height: var(--e-global-typography-48312dc-line-height);
}

body .login-form-shortcode .um-form .um-field input::placeholder {
  color: var(--e-global-color-text) !important;
  opacity: 1; /* Firefox 
}

body .login-form-shortcode .um-form .um-field input::-ms-input-placeholder {
  /* Edge 12 -18 */
  color: var(--e-global-color-text) !important;
}

.login-form-shortcode .um-form .um-row {
  margin-bottom: 16px !important;
}
.login-form-shortcode .um-form .um-col-alt {
  margin: 0px !important;
}
.login-form-shortcode .um-form .um-col-alt #um-submit-btn {
  width: 100% !important;
  padding: 6px 6px !important;
  border-radius: 0px !important;
  color: var(--e-global-color-dc92533) !important;
  background: #2a78b8 !important;
  border-color: #2a78b8 !important;
  font-family: var(--e-global-typography-c2636a5-font-family), Sans-serif;
  font-size: var(--e-global-typography-48312dc-font-size);
  line-height: var(--e-global-typography-48312dc-line-height);
}

.login-form-shortcode .um-form .um-col-alt #um-submit-btn:hover {
  background-color: #24669c !important;
  border-color: #24669c;
}

body .login-form-shortcode .um-form .um-col-alt-b {
  padding-top: 16px;
}

body .login-form-shortcode .um-form .um-col-alt-b .um-link-alt {
  text-align: left;
  color: #9c120f;
  font-family: var(--e-global-typography-c2636a5-font-family), Sans-serif;
  font-size: var(--e-global-typography-48312dc-font-size);
  line-height: var(--e-global-typography-48312dc-line-height);
}

body .login-form-shortcode .um-form .um-col-alt-b .um-link-alt:hover {
  text-decoration: none;
}

.um-login.um-loading:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.85);
	z-index: 2;
}
.um-login.um-loading:after {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	width: 50px;
	height: 50px;
	margin: -25px 0 0 -25px;
	border: 3px solid #f3f3f3;
	border-radius: 50%;
	border-top: 3px solid #3498db;
	animation: um-spin 1s linear infinite;
	z-index: 3;
} 

@keyframes um-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

body p > a,
body div > a{
	color: var( --e-global-color-b07f7d2 );
}
body p > a:hover,
body div > a:hover{
   color: var( --e-global-color-091a3ba );
}

/* Tooltip style */
.tooltip-show {
  position: relative;
  cursor: pointer;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  line-height: 22px;
}

.tooltip-show > span {
  display: flex;
}

.tooltip-show > span > img {
  width: 18px;
}

.tooltip {
  visibility: hidden;
  width: 200px;
  background-color: var(--e-global-color-c668341);
  color: var(--e-global-color-dc92533);
  text-align: center;
  border-radius: 5px;
  padding: 4px 10px;
  position: absolute;
  z-index: 1;
  margin-bottom: -20px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 14px;
  line-height: 20px;
  top: -60%;
  right: -140px;
}

.elementor-widget-ucaddon_customer_partner_post_grid .ue-item .tooltip {
  top: 15%;
  right: 0px;
  left: 0px;
  margin: auto;
}

.elementor-widget-ucaddon_customer_partner_post_grid .ue-item .tooltip:after {
  top: unset;
  bottom: -10px;
  right: 50%;
  border-color: var(--e-global-color-c668341) transparent transparent
	transparent;
}

body .latest-news-wrp .tooltip {
  top: -100px;
  left: 30%;
}

.latest-news-wrp .owl-carousel .owl-stage-outer {
  overflow-y: visible;
  overflow-x: clip;
}

body #uc_post_carousel_elementor_32fe30b .uc_carousel_item {
  overflow: visible;
}

.tooltip::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent var(--e-global-color-c668341) transparent
	transparent;
}

.latest-news-wrp .tooltip::after {
  top: unset;
  bottom: -10px;
  right: 50%;
  border-color: var(--e-global-color-c668341) transparent transparent
	transparent;
}

.tooltip-show:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

/* Form Fields */
.section-divider {
  background: var(--e-global-color-0ac5838);
  margin: 0;
  padding: 8px 8px;
  font-weight: 400;
  font-size: 1.25rem;
  line-height: 2;
  font-family: "din-2014", Sans-serif;
  width: 100%;
}

.section-divider .section-number {
  background: rgba(255, 255, 255, 0.95);
  color: var(--e-global-color-2525440);
  font-weight: 700;
  display: inline-block;
  width: 2em;
  border-radius: 50%;
  text-align: center;
  height: 2em;
  line-height: 2em;
  font-size: 0.75em;
  vertical-align: middle;
  margin-right: 1em;
  margin-top: -3px;
}




/* Global Form */
body .elementor-form .elementor-form-fields-wrapper .elementor-field-type-html {
  background: var(--e-global-color-0ac5838);
  margin: 0;
  padding: 8px 8px !important;
  font-weight: 300;
  font-size: 1.25rem;
  line-height: 2;
  font-family: "din-2014", Sans-serif;
  margin-bottom: 16px;
  margin-top: 32px;
}

body .elementor-form .elementor-field-group {
  margin-bottom: 16px;
}

body
  .elementor-form
  .elementor-form-fields-wrapper
  .elementor-field-type-html:first-child {
  margin-top: 0px;
}

.elementor-form
  .elementor-form-fields-wrapper
  .elementor-field-type-html
  .number {
  background: rgba(255, 255, 255, 0.95);
  color: var(--e-global-color-2525440);
  font-weight: 700;
  display: inline-block;
  width: 2em;
  border-radius: 50%;
  text-align: center;
  height: 2em;
  line-height: 2em;
  font-size: 0.75em;
  vertical-align: middle;
  margin-right: 1em;
  margin-top: -3px;
}

.elementor-form .elementor-form-fields-wrapper .elementor-field-label {
  font-size: 16px;
  line-height: 22px;
  color: var(--e-global-color-c668341);
  margin-bottom: 0.5rem;
  font-family: "din-2014", Sans-serif;
}

body .elementor-form .elementor-form-fields-wrapper input,
body.elementor-form .elementor-form-fields-wrapper textarea,
body .elementor-form .elementor-form-fields-wrapper select {
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  border: 1px solid var(--e-global-color-0fc47c7);
  border-radius: 0;
  line-height: 1.4;
}

body .elementor-field-type-acceptance .elementor-field-option {
  display: flex;
  align-items: center;
  gap: 10px;
}

body .elementor-form .elementor-button[type="submit"] {
  color: var(--e-global-color-c668341);
  font-size: 16px;
  line-height: 22px;
  font-family: "din-2014", Sans-serif;
  margin-top: 1.2rem !important;
}

body .elementor-form span.elementor-message.elementor-message-danger {
	min-height: 22px;
	width: 100%;
}

body .elementor-form .elementor-button[type="submit"]:hover {
  color: var(--e-global-color-dc92533);
  background-color: var(--bs-btn-hover-border-color);
  border-color: var(--bs-btn-hover-border-color);
}

.elementor-field-group-account_info {
  margin-top: 20px;
}

#customer_registration .elementor-field-group:nth-child(3),
#reseller_partner .elementor-field-group:nth-child(3),
#customer_registration .elementor-field-group:nth-child(4),
#reseller_partner .elementor-field-group:nth-child(4){
  margin-top: 16px;
}

body input[type="text"]:focus,
.um .um-form input[type="password"]:focus,
.um .um-form input[type="text"]:focus,
body input[type="password"]:focus,
body input[type="number"]:focus {
  box-shadow: 0 0 0 0.25rem rgba(156, 18, 15, 0.25);
}


/*.captcha-image{*/
/*    text-align: left;*/
/*}*/


.elementor-message {
  display: inline-block;
  margin-bottom:0px;
}
.elementor-message:before {
	content:unset !important;
}

body .elementor-field-type-submit .elementor-button{
	width:100%;
}


body .elementor-field-type-submit .elementor-button:focus {
  background-color: transparent;
  color: #FFFFFF;
  border-style: solid;
  border-color: var( --e-global-color-text );
}


#customer_registration .elementor-column{
  display: block;
}

label > a {
  color: var( --e-global-color-b07f7d2 );
}


/*.elementor-field-group-image_captcha {*/
/*  margin: 0px !important;*/
/*}*/

/* newsletter-form */
body .news-letter-form-wrp .elementor-form .elementor-button[type="submit"] {
  margin: 0px;
}

body .news-letter-form-wrp .elementor-form .elementor-field-group input {
  min-height: 36px;
  height: unset;
  padding: 0px 15px;
}

body .news-letter-form-wrp .elementor-form .elementor-button[type="submit"] {
  margin: 0px;
  background: transparent;
  color: var(--e-global-color-dc92533);
  border: 1px solid;
  min-height: 36px;
}

body
  .news-letter-form-wrp
  .elementor-form
  .elementor-button[type="submit"]:hover {
  background: var(--e-global-color-dc92533);
  color: var(--e-global-color-c668341);
}

.current-language-flag {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-language-flag .language-button {
  padding: 0px;
  background: transparent;
  font-size: 16px;
  border: unset;
  line-height: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}

.current-language-flag .language-button:hover,
.current-language-flag .language-button:focus {
  background: transparent;
  border: unset;
  color: var(--e-global-color-c668341);
}

.current-language-flag .language-button > img {
  width: 16px;
}

body .search-form input.search-field,
body .search-form label {
  width: 100%;
}

header .custom-logo-link img {
  width: 253px;
}

.main-header-bar-wrap {
  z-index: 1;
}

.heading-background .elementor-widget-container:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: var(--e-global-color-b07f7d2);
  mix-blend-mode: multiply;
  z-index: 0;
}

.heading-background .elementor-widget-container > h1 {
  position: relative;
}

/* three column Steps  */
.three-steps-iconbox .elementor-icon-box-content {
  display: flex;
  flex-direction: column-reverse;
}
.three-steps-iconbox .elementor-icon-box-content .elementor-icon-box-title {
  margin: 0;
}

/* Accordion With Count */
.accordion-with-count .e-n-accordion-item:before {
  top: 20px;
  position: absolute;
  content: "- 0" counter(my-awesome-counter);
  transform: rotate(270deg);
  font-size: 16px;
  line-height: 22px;
  color: #adb5bd;
}

.accordion-with-count .e-n-accordion-item {
  position: relative;
  counter-increment: my-awesome-counter;
}

/* Custom Bullets */
body .custom-bullets-style ul {
  margin: 0;
  list-style: none;
  padding-left: 20px;
}
.custom-bullets-style ul li {
  position: relative;
  padding-left: 10px;
  margin-bottom: 8px;
}
.elementor-icon-box-title {
  margin: 0 !important;
}

.custom-bullets-style ul li:before {
  content: "\f055";
  position: absolute;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  color: var(--e-global-color-b07f7d2);
  font-size: 0.8em;
  left: -19px;
  top: 0px;
}
.custom-bullets-style ul ul li:before {
	font-weight: 400;
}
.custom-bullets-white.custom-bullets-style ul li:before {
  color: var(--e-global-color-dc92533);
}


/* Custom Icon Box */
.custom-icon-box-circle .elementor-icon-box-icon > span {
  width: 120px;
  height: 120px;
  background: var(--e-global-color-b07f7d2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
}

.custom-icon-box-circle.vertical-style
  .elementor-icon-box-wrapper
  .elementor-icon-box-content {
  padding: 0px 3rem;
}

.custom-icon-box-circle .elementor-icon-box-icon > span > i {
  z-index: 10;
}
body .custom-icon-box-circle .elementor-icon-box-wrapper {
  align-items: center !important;
}

@media (min-width: 768px) {
  .custom-icon-box-circle .elementor-icon-box-icon > span {
	width: 150px;
	height: 150px;
  }
}


@media (min-width: 768px) {
  .custom-icon-box-circle.w100 .elementor-icon-box-icon > span {
	width: 100px;
	height: 100px;
  }
}

.custom-li-arrow-right .elementor-icon-list-item .elementor-icon-list-icon {
  order: 2;
  padding-left: 10px;
}
.overview-button-rotate .elementor-button-icon {
  transform: rotate(300deg);
}

/* Custom CTA SECTION */

body .custom-cta-section {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  height: 100%;
}

body .custom-cta-section .elementor-cta__bg-wrapper {
  width: auto;
  margin: auto;
  border-radius: 50%;
}
.custom-cta-section .elementor-cta__bg {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

/* Custom Testimonials */
body .custom-testimonials-shadow .swiper-slide {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
body .elementor-testimonial__cite {
  flex-direction: row;
  justify-content: center;
  gap: 5px;
}

/* Language Popup */
.language-popup-styling ul {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  row-gap: 16px;
}
.language-popup-styling ul > li {
  width: 20%;
  position: relative;
}

.language-popup-styling ul > li .wpml-ls-link {
  border-left: 5px solid var(--e-global-color-secondary);
  padding: 0px !important;
  padding-left: 10px !important;
  color: var(--e-global-color-dc92533);
}
body .wpml-ls-current-language > a {
  border-color: #198754 !important ;
}
.elementor-button-icon {
  display: flex;
  align-items: center;
}
.header-button-popup .elementor-button-icon svg {
  transform: rotate(270deg);
}

/* 
Newsletter styling */
body .newsletter-form-wrp .elementor-form .elementor-field-type-text {
  width: 70%;
  padding: 0px;
}
body .newsletter-form-wrp .elementor-form .elementor-field-type-submit {
  width: 30%;
  padding: 0px;
}
.newsletter-form-wrp .elementor-form .elementor-field-type-acceptance {
  order: 3;
  flex: 1;
}


body
  .newsletter-form-wrp
  .elementor-form
  .elementor-field-type-submit
  .elementor-button {
  margin: 0px !important;
  color: var(--e-global-color-dc92533);
}

/*Popup Sidebar Styling*/
.sidebar-navigation-popup .sidebar-title {
  font-size: 20px;
  line-height: 24px;
  font-weight: 600;
}

.sidebar-navigation-popup .sidebar-menu-styling .menu {
  list-style: none;
  padding-left: 20px;
  padding-right: 20px;
}
.sidebar-navigation-popup .sidebar-menu-styling .menu > .menu-item,
.sidebar-menu-styling ul > .menu-item,
.elementor-nav-menu--dropdown .menu-item {
  border-bottom: 1px solid var(--e-global-color-8807009);
}
.sidebar-navigation-popup
  .sidebar-menu-styling
  .menu
  > .menu-item
  .elementor-sub-item,
.elementor-nav-menu--dropdown .menu-item .elementor-sub-item {
  padding: 8px;
  display: inline-block;
  font-size: 16px;
  line-height: 22px;
  font-weight: 600;
  color: var(--e-global-color-c668341);
  width: 100%;
}
body .elementor-nav-menu--dropdown .elementor-item.elementor-item-active,
body .elementor-sub-item.elementor-item-active {
  color: var(--e-global-color-dc92533) !important;
}
body .elementor-nav-menu--dropdown .menu-item .elementor-sub-item {
  padding: 14px !important;
  color: var(--e-global-color-dc92533);
  border-inline-start: unset;
}

.mega-menu-dropdown .sidebar-menu-styling ul > .menu-item > a {
  justify-content: space-between;
}
.sidebar-navigation-popup
  .sidebar-menu-styling
  .menu
  > .menu-item:hover
  .elementor-sub-item:not(.elementor-item-active) {
  background: #f8f9fa;
}

.elementor-nav-menu li {
  position: unset !important;
}

.sidebar-navigation-popup .sidebar-menu-styling .menu > .menu-item h2,
.elementor-nav-menu--dropdown .menu-item h2 {
  font-size: 16px;
  line-height: 22px;
  font-weight: 700;
  margin: 32px 0px 8px 5px;
}

body .elementor-nav-menu--dropdown .menu-item h2 {
  color: var(--e-global-color-dc92533);
  padding-left: 7px;
}

.elementor-nav-menu .elementor-nav-menu--dropdown {
  visibility: hidden !important;
}

body .mega-menu-dropdown .elementor-nav-menu .elementor-nav-menu--dropdown {
  visibility: visible !important;
  top: 0px;
  width: 100%;
  margin-left: 0px;
  display: block;
  visibility: hidden;
  right: -1000px;
  z-index: 10;
  margin-top: 0px;
  background: var(--e-global-color-0fc47c7);
  box-shadow: unset;
  height: 80vh;
  overflow: scroll;
}

.sidebar-menu-styling .elementor-sub-item:focus {
  background: transparent;
}

.sidebar-menu-styling .elementor-sub-item:focus {
  color: var(--e-global-color-c668341) !important;
}


#search_field_wrp .elementor-widget-search-form {
  margin: auto;
}
body #search_field_wrp .elementor-search-form__submit {
  background: transparent;
}
#search_field_wrp .elementor-search-form__container {
  background: transparent;
  display: flex;
  gap: 0;
}
#search_field_wrp .elementor-search-form__input {
  background: var(--e-global-color-dc92533);
  border-radius: 0px;
}
body #search_field_wrp .elementor-search-form__submit {
  background: transparent;
  width: 151px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

#search_field_wrp .elementor-search-form__submit .elementor-button-icon {
  order: -1;
}
[type="button"]:focus:not(:focus-visible),
[type="submit"]:focus:not(:focus-visible),
button:focus:not(:focus-visible) {
  outline: none;
}
.elementor-kit-7 button:focus {
  border-color: transparent;
}

.page-header {
  display: none;
}
body .mega-menu-dropdown {
  overflow: hidden;
}

body .inline-two-btn {
  width: 70vw;
  z-index: 10;
}

body .featured-content-slider .swiper-pagination .swiper-pagination-bullet {
  margin-right: 0px;
  margin-left: 4px;
}

body .main-menu-horizontal .sub-arrow {
  display: none;
}

/*Post Skin*/
.elementor-post__title {
  width: 100%;
  display: flex;
  flex-direction: row;
  gap: 20px;

}


.main-menu-horizontal .elementor-nav-menu li:first-child a:before {
  content: unset;
}

.icon-box-list-style .elementor-icon-box-icon {
  background: #555;
  border-radius: 50%;
  width: 75px;
  height: 75px;
  align-items: center;
  justify-content: center;
}

/*Custom Country List*/

.custom-country-list .ue_taxonomy {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}
.custom-country-list .ue_taxonomy .ue_taxonomy_item {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.custom-country-list .ue_taxonomy .ue_taxonomy_item .ue_taxonomy_image img {
  width: 260px;
  display: block;
}

.Video-post-grid-wrp .ue_post_grid_item,
.professor-videos-wrp .uc_post_grid_style_one_item {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  cursor: pointer;
}

.Video-post-grid-wrp .ue-grid-item-category {
  position: absolute;
  right: 24px;
  top: 20px;
}

.Video-post-grid-wrp .uc_content-info-wrapper {
  display: flex;
  flex-direction: column;
}


.Video-post-grid-wrp .uc_content-info-wrapper .post-content {
  margin-top: 4px;
}


.upcoming-events-wrp .elementor-post__text .event-end-date{
  width: 40%;
	
}

.professor-videos-wrp .uc_post_title,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .ue_p_title,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .ue_p_title{
  transition: all 0.8s ease;
  position: absolute;
  top: 0px;
  padding: 24px 16px 8px 16px;
  left: 20px;
  margin: 0px;
}
.elementor-widget-ucaddon_sc_image_overlay_post_grid .ue_p_title,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .ue_p_title{
  margin: 0px !important;
}

.professor-videos-wrp .uc_post_title a,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .ue_p_title > span,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .ue_p_title > span{
  position: relative;
  color: var(--e-global-color-dc92533);
}

.professor-videos-wrp .uc_post_title:before,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .ue_p_title:before,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .ue_p_title:before{
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: var(--e-global-color-b07f7d2);
  mix-blend-mode: multiply;
  z-index: 0;
}

.professor-videos-wrp .uc_content,
.ue-custom-taxonomies-box .ue_taxonomy_item_content,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .uc_content,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .uc_content{
  width: 100%;
  height: auto;
  bottom: -3rem;
  transform: translateY(100%) skewY(-10deg);
  color: #fff !important;
  transition: all 0.8s ease;
  position: absolute;
}

.professor-videos-wrp .ue_post_grid_item:hover .uc_content,
.ue-custom-taxonomies-box:hover .ue_taxonomy_item_content,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .ue-item:hover .uc_content,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .ue-item:hover .uc_content
{
  bottom: -1.7rem;
  transform: translateY(0%) skewY(-5deg);
}
.professor-videos-wrp .uc_post_title,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .ue_p_title,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .ue_p_title{
  transition: all 0.8s ease;
}

.professor-videos-wrp .ue_post_grid_item:hover .uc_post_title,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .ue-item:hover .ue_p_title,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .ue-item:hover .ue_p_title{
  padding-top: 0.5rem !important;
  transition: all 0.8s ease;
  padding-bottom: 0.5rem !important;
}

.professor-videos-wrp .uc_content .uc_content_inner,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .uc_content .uc_content_inner,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .uc_content .uc_content_inner{
  overflow: hidden;
  transform: skewY(5deg);
}
.professor-videos-wrp .uc_content .uc_content_inner .uc_post_button {
  opacity: 0;
  transition: all 0.8s ease;
}

.professor-videos-wrp .ue_post_grid_item:hover .uc_post_button {
  opacity: 1;
  transition-delay: 0.8s;
}

.Technology-partners-wrp .wp-caption-text {
  border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #212529;
  transition: all 0.3s;
}

.Technology-partners-wrp .wp-caption-text:hover {
  color: var(--e-global-color-dc92533) !important;
  background-color: #212529;
  transition: all 0.3s;
  cursor: pointer;
}

.ue-meta-data.custom-meta-fields-wrp {
  flex-direction: column;
  flex-wrap: unset;
}
.ue_post_accordion_content_in {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(1, 1fr);
  row-gap: 60px;
}
.ue-meta-data.custom-meta-fields-wrp * {
  color: var(--e-global-color-c668341);
  font-size: var(--e-global-typography-48312dc-font-size);
  line-height: var(--e-global-typography-48312dc-line-height);
}
.btn-wrp {
  background: #555;
  align-self: flex-start;
  padding: 4px 8px;
  border: 1px solid #555;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
	border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.btn-wrp > a {
  color: #fff !important;
  font-size: 14px !important;
  line-height: 20px !important;
}
.btn-wrp > a > i {
  color: #fff !important;
  margin-left: 10px;
  font-size: 14px !important;
}

.btn-wrp:hover {
  color: #fff;
  background-color: #484848;
  border-color: #484848;
}

#uc_reseller_netword_post_accordion_elementor_58b2233 .uc_container .uc-heading {
  justify-content: space-between;
}



/*Getting Cam Parts*/
.color-red .elementor-icon,
.color-yellow .elementor-icon,
.color-blue .elementor-icon{
  background: #fff;
  border-radius: 50%;
  padding: 13px;
}

.color-red svg g path{
	stroke:#E31818;
}

.color-yellow svg g path{
	stroke:#FAA21B;
}

.color-blue svg g path{
	stroke:#337AB7;
}



/*Add To Calendar*/
.calendar-toggle {
  padding: 4px 8px !important;
  font-size: 14px;
  line-height: 20px !important;
  display: flex;
  align-items: center;
  gap: 8px;
  border-color:#212529 !important;
  position:relative;
}

.calendar-toggle:after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}


.overflow-hidden {
  overflow: hidden !important;
}

.calendar-toggle:hover,
.calendar-toggle:focus{
  color: #fff;
  background-color: #212529 !important;;
  border-color: #212529 !important;;
}


.calendar-options > a > i {
  margin-right: 8px;
}

.calendar-dropdown {
  position: relative;
}


   .calendar-options {
	margin-top: 10px;
	border: 1px solid #ccc;
	background: #fff;
	position: absolute;
	z-index: 1000;
	border-radius: 8px;
	padding: 8px 0px;

	}
	
	.add-to-calendar .calendar-options {

		width: 270px;
		right: 0px;
	}
	
	
	.calendar-toggle {
		cursor: pointer;
	}


.calendar-options > a {
  display: inline-block;
  font-size: 16px;
  line-height: 22px;
  color: #000000;
  padding: 4px 16px;
  width: 100%;
}


.calendar-options > a:hover {
background: #f8f9fa;
  color: #000000;
}



.red-column,
.yellow-column,
.blue-column{
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15) !important;
}


.software-details .year-display {
  font-size: var(--e-global-typography-cd74812-font-size);
  line-height: var( --e-global-typography-cd74812-line-height);
  color: var(--e-global-color-c668341);
  margin: 0px 0px 8px;
  font-weight: 700;
}


.middle-content {
  display: flex;
  justify-content: space-between;
}
.middle-content .languages {
  margin: 0px;
  font-size: var(--e-global-typography-a80dbdc-font-size);
  line-height: var(--e-global-typography-a80dbdc-line-height);
}
.middle-content .download-button {
  border-radius: 50%;
  width: 60px;
  height: 60px;
  text-align: center;
}
.middle-content .download-button svg {
  width: 30px;
}

.middle-content .download-button svg g path {
  stroke: #fff;
}

.footer-content {
  border-top: 1px solid #dee2e6;
  margin-top: 24px;
  padding-top: 16px;
}
.footer-content .version-select {
  border: 1px solid #212529;
  border-radius: 0;
  padding: 4px 8px;
  background: #fff;
}
.footer-content .version-select:hover {

  background: #212529;
  color: #fff;
}



/*Professor Taxonomies*/

.ue-custom-taxonomies-box .ue_taxonomy_item_title:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: var(--e-global-color-b07f7d2);
  mix-blend-mode: multiply;
  z-index: 0;
}

.ue-custom-taxonomies-box .ue_taxonomy_image {
  position: relative;
}

.ue-custom-taxonomies-box .ue_taxonomy_item_title {
  transition: all 0.8s ease;
  position: absolute;
  top: 0px;
  padding: 24px 16px 8px 16px;
  left: 20px;
  margin: 0px;
}

.ue-custom-taxonomies-box :hover .ue_taxonomy_item_title {
  padding-top: 0.5rem !important;
  transition: all 0.8s ease;
  padding-bottom: 0.5rem !important;
}
.ue-custom-taxonomies-box .ue_taxonomy_item_title span {
  position: relative;
}

.ue-custom-taxonomies-box {
  position: relative;
  overflow: hidden;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.ue_taxonomy,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .uc_content{
  display: flex;
  gap: 32px;
}
.ue_taxonomy_item.uc-is-parent {
  width: 100%;
}

.ue-custom-taxonomies-box .ue_taxonomy_item_content,
.elementor-widget-ucaddon_sc_image_overlay_post_grid .uc_content,
.elementor-widget-ucaddon_sc_image_overlay_page_grid .uc_content{
  flex-direction: column !important;
  padding: 32px 16px 48px 16px !important; 
  background-color: #000000cc !important;
}
.ue-custom-taxonomies-box .ue_taxonomy_item_content .uc_content_inner {
  overflow: hidden;
  transform: skewY(5deg);
  width: 100%;
}

.ue-custom-taxonomies-box
  .ue_taxonomy_item_content
  .uc_content_inner
  .uc_post_button,
  .photo-links-wrp,
  .elementor-widget-ucaddon_sc_image_overlay_page_grid .uc_content_inner .uc_post_button
  {
  opacity: 0;
  transition: all 0.8s ease;
}

.ue-custom-taxonomies-box:hover
  .ue_taxonomy_item_content
  .uc_content_inner
  .uc_post_button,
  .elementor-widget-ucaddon_sc_image_overlay_post_grid .ue-item:hover .photo-links-wrp,
  .elementor-widget-ucaddon_sc_image_overlay_page_grid .ue-item:hover .uc_post_button{
  opacity: 1;
  transition-delay: 0.8s;
}

.photo-links-wrp ul {
  list-style: none;
  padding-left: 0px;
}
.photo-links-wrp ul li a {
  display: flex;
  gap: 8px;
  align-items: center;
  color: var(--e-global-color-dc92533);
}



.ue-custom-taxonomies-box
  .ue_taxonomy_item_content
  .uc_content_inner
  .uc_post_button
  .uc_btn_inner {
  margin-top: 10px;
  font-family: var(--e-global-typography-48312dc-font-family), Sans-serif;
  font-size: var(--e-global-typography-48312dc-font-size);
  line-height: var(--e-global-typography-48312dc-line-height);
  display: flex;
  gap: 6px;
}

.Video-post-grid-wrp .uc_post_grid_style_one_image {
  padding: 24px;
}

.Video-post-grid-wrp.no-padding .uc_post_grid_style_one_image {
  padding: 0px;
}

.logos img {
  object-fit: unset !important;
  width: auto !important;
}

.video-info {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  font-size: 14.4px;
  color: var(--e-global-color-c668341);
  margin-bottom: 4px;
}
.video-info .info-wrp {
  display: flex;
  gap: 6px;
  align-items: center;
}

.legal-notice {
  transition: all 1s ease;
  font-size: 14px;
  position: absolute;
  z-index: 1;
  top: initial;
  bottom: 2.5%;
  width: 95%;
  height: fit-content;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  left: 50%;
  transform: translate(-50%, 0);
  opacity: 0;
}

.legal-notice i {
  margin-right: 4px;
}

.ue_post_grid_item.play-video:hover .legal-notice {
  transition: all 1s ease;
  opacity: 1;
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: none;
  padding: 10px 20px;
  font-size: 24px;
  cursor: pointer;
  border-radius: 50%;
  transition: color 0.3s ease;
  color: rgba(0, 0, 0, 0.7);
}
.lity-container {
  max-width: 92% !important;
}

.Video-post-grid-wrp .ue_post_grid_item:hover .video-play-button {
  color: #dc3545;
  transition: all 1s ease;
}

.lity-close {
  cursor: pointer !important;
  height: auto !important;
  margin: 0;
  padding: 4.5px 16px !important;
  transition: color 0.2s !important;
  visibility: inherit;
  width: auto !important;
  background: rgba(30, 30, 30, 0.6) !important;
  border: 0px !important;
  text-shadow: unset !important;
  color: #ccc !important;
  font-size: 24px !important;
}

.lity-close:hover {
  color: #fff !important;
}

.lity {
  background: rgba(0, 0, 0, 0.7) !important;
}
.lity-content::after {
  content: unset !important;
}

.lity-close > svg {
  display: block;
  overflow: visible;
  position: relative;
  width: 24px;
  height: 24px;
}


.video-play-button:after {
  content: "";
  position: absolute;
  left: 50%;
  top: 52%;
  transform: translate(-39%,-55%);
  width: 0;
  height: 0;
  border-left: 20px solid #fff;
  border-bottom: 13px solid transparent;
  border-top: 11px solid transparent;
}


body .ue-grid-item-category a {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ue-meta-data.custom-meta-fields-wrp .email > a,
.ue-meta-data.custom-meta-fields-wrp .internet > a {
  color: var(--e-global-color-b07f7d2);
}

.ue-meta-data.custom-meta-fields-wrp .email > a:hover,
.ue-meta-data.custom-meta-fields-wrp .internet > a:hover {
  color: var(--e-global-color-b07f7d2);
}

body .ue_taxonomy.uc-grid-filter > a {
  margin-right: 0px !important;
}

.custom-testimonials-shadow .elementor-main-swiper.swiper {
  padding: 40px 10px;
}

.elementor-widget-n-tabs .e-n-tabs-heading {
  border-bottom: 1px solid #adb5bd;
}

.elementor-widget-n-tabs .e-n-tabs-heading > button[aria-selected="true"] {
  border-bottom-color: transparent !important;
}

.no-border.elementor-widget-n-tabs .e-n-tabs-heading {
  border-bottom: unset !important;
}

.elementor-page-title .elementor-widget-container {
  padding-bottom: 16px !important;
}

.no-padding-bottom.elementor-page-title .elementor-widget-container{
	padding-bottom:0px !important;
}

.elementor-widget-n-accordion
  .e-n-accordion-item[open]
  .e-n-accordion-item-title {
  border-bottom-color: #495057 !important;
}

.accordion-with-count .e-n-accordion-item > div:nth-child(2) {
  padding: 0px 20px 32px 40px;
}


.elementor-repeater-item-d56e22a {
  background-color: rgb(248, 249, 250);
  box-shadow: rgba(0, 0, 0, 0.075) 0px 2px 4px 0px;
  padding: 4.2px 8px !important;
  border-radius: 50rem;
  font-size: 12px !important;
}

.related-files {
  margin-top: 8px;
}
.related-files ul {
  padding: 0px;
  list-style: none;
}
.related-files ul > li {
  padding: 8px 16px;
  border-top: 1px solid #dee2e6;
}

.related-files ul > li > a {
  display: flex;
  gap: 8px;
  align-items: center;
  color: #9c120f;
  font-size: 14px;
  line-height: 22px;
}

.warning-message {
  background-color: rgb(156, 18, 15);
  box-shadow: rgba(0, 0, 0, 0.075) 0px 2px 4px 0px;
  color: #fff;
  font-size: 12px;
  left: 20px;
  line-height: 12px;
  margin-bottom: 16px;
  overflow-wrap: break-word;
  position: absolute;
  top: 20px;
  border-radius: 2rem;
  padding: 4.2px 7.8px;
  display: flex;
  gap: 8px;
  align-items: center;
}

.subscription-access {
  filter: grayscale(100%);
  opacity: 0.6;
}

body .open-login-popup {
  background-color: rgba(0, 0, 0, 0) !important;
  box-sizing: border-box;
  color: rgb(248, 249, 250) !important;
  font-family: "din-2014";
  font-size: 14px;
  hyphens: manual;
  line-height: 19.6px;
  padding: 4px 8px !important;
  border-color: #fff !important;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  margin: 4px auto 0px;
}

body .open-login-popup:hover {
  background: #fff !important;
  color: #000 !important;
}

.custom-testimonials-shadow .swiper-pagination {
  bottom: -16px !important;
}

.elementor-widget-image img {
  width: 100%;
}

.bg-white.elementor-sticky--active {
  background: #fff !important;
}

body .custom-testimonials-shadow .swiper-wrapper {
  align-items: flex-start;
}

.custom-li-arrow-right .elementor-icon-list-item:hover {
  background-color: #f8f9fa;
}

.custom-li-arrow-right .elementor-icon-list-item:after {
  border-color: #dee2e6 !important;
}

.custom-li-arrow-right .elementor-icon-list-item a {
  color: var(--e-global-color-text);
}
.custom-li-arrow-right .elementor-icon-list-item {
  margin: 0px !important;
  padding: 8px 0px !important;
}

.swiper-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.swiper-pagination .swiper-pagination-bullet {
  margin: 0px !important;
}

.no-link-access {
  pointer-events: none;
  cursor: default;
  text-decoration: none;
}
.customer-rating-search button:focus {
  background: transparent !important;
	color: #212529 !important;
}


.custom-stars-filter .uc-checkbox-star-filter {
  align-items: center;
  justify-content: flex-start;
}
.custom-stars-filter .uc-checkbox-star-filter .ue_star_filter {
  align-self: center;
  flex: 1;
}

.custom-testimonials-box  .masonryPostGridItem {
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15) !important;
}

.testimonials-info {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 16px;
  color: #6C757D;
}
.testimonials-info .title::before {
  content: "— ";
}
.testimonials-info .title::after {
  content: " |";
}


.custom-testimonials-box .uc_stars > i {
  color: #ffcb00;
  font-size: 14px;
}

.archive_pagination .page-numbers:first-child {
  border-radius: 8px 0px 0px 8px !important;
  width:42px !important;
	border-left: 1px solid #DEE2E6 !important;
}

.archive_pagination .page-numbers:last-child {
  border-radius: 0px 8px 8px 0px !important;
  width:42px !important;
}
.archive_pagination .page-numbers{
	  width:32px;

}


.uc-search-filter__input:focus-visible {
  outline: 0px;
}

.uc-search-filter-btn:focus-visible {
  outline: 0px;
}
.uc-search-filter-btn:focus {
  background: transparent !important;
}

.uc-search-filter-btn:focus:hover {
	  color: #212529 !important;
  border-color: #212529 !important;
}


.uc-search-filter-btn:focus:hover {
	  color: #212529 !important;
  border-color: #212529 !important;
}


.uc-search-filter-btn:focus:hover .ue-btn-icon {
		  color: #212529 !important;

}

.uc-search-filter-btn:hover{
	color:#212529;
}

.doc-accodion-wrp .ue_taxonomy.uc_material_accordion {
  display: unset;
}
.doc-accodion-wrp .ue_taxonomy.uc_material_accordion .ue_taxonomy_item_num_posts {
  background: red;
  padding: 2px 9px;
  border-radius: 0.375rem;
  background: rgba(25, 135, 84, 0.5);
  margin-left:16px;
}

.doc-accodion-wrp .ue_taxonomy.uc_material_accordion .ue_heading_title {
  margin: 0px;
}


.open-doc-btn {
  color: var( --e-global-color-c668341 );
  font-size: var( --e-global-typography-48312dc-font-size );
  line-height: var( --e-global-typography-48312dc-line-height );
}


/*custom Calendar Widget*/

.custom-calendar-widget .month_browser {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.custom-calendar-widget .month_browser .col.current {
  font-size: var(--e-global-typography-a80dbdc-font-size);
  line-height: var(--e-global-typography-a80dbdc-line-height);
}

.custom-calendar-widget table td, 
.custom-calendar-widget table th{
	border:unset !important;
	padding:0px !important;
}



.custom-calendar-widget table tbody > tr:nth-child(2n+1) > td {
	background:transparent;
}

.custom-calendar-widget table tbody tr:hover > td, 
.custom-calendar-widget table tbody tr:hover > th{
		background:transparent;

}

.custom-calendar-widget table tbody .day-cell > div{
	text-align:center;
	font-size: 16px;
	color: #000;
	box-shadow: unset;
	border-radius: 50%;
	outline-style:solid;
	outline-width:1px;
	outline-color:transparent;
	width: 56px;
	padding: 16px 15px;


}

.custom-calendar-widget table tbody .day-cell > div.has-events {
  border-radius: 50%;
  border-color: #555555 !important;
  text-align: center;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075) !important;
  position: relative;
}

.post-data {
  position: absolute;
  background: #000;
  color: #ffffff;
  border-radius: 8px;
  display: flex;
  opacity: 0;
  visibility: hidden;
  top: 100%;
  width: 110px;
  padding: 8px 12px;
  text-align: left;
  font-size: 14px;
  line-height: 18px;
  font-weight: 600;
  right: -60%;
  flex-direction: column;
  justify-content: flex-start;
}

.custom-calendar-widget table tbody .day-cell > div.has-events:hover  .post-data{
  opacity: 1;
  visibility: visible;

}

.post-data:before {
  content: "";
  position: absolute;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  top: -5px;
  right: 50%;
  border-color: transparent transparent #000 transparent;
}

.event-count {
  position: absolute;
  top: 0px;
  right: -15px;
  transform: translate(-50%,-50%) !important;
  background: var(--e-global-color-primary);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  
}

.event-tooltip {
	position: absolute;
	background: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 5px;
	border-radius: 4px;
	max-width: 200px;
	z-index: 9999;
	box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3);
}

.day-cell > div.has-events {
	cursor: pointer;
	position: relative;
}

.day-cell > div:hover {
	outline-color:var(--e-global-color-primary) !important;

}

 .day-cell > div.current-day{
	 outline-color:var(--e-global-color-primary) !important;
 }
 

 /*Select 2*/
 .select2-selection {
  border-radius: 0px !important;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  border: 1px solid var(--e-global-color-0fc47c7) !important;
  height: 40px !important;
  display: flex !important;
}
.select2-selection .select2-selection__rendered {
  padding: 0px !important;
}
.select2-selection__arrow {
  top: 8px !important;
}
.select2-selection__arrow b {
  border:unset !important;
}
.select2-selection {
  padding: .375rem 2.25rem .375rem .75rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right .75rem center;
  background-size: 16px 12px;
}
.select2-dropdown{
	top:32px !important;
}


/*Custom License Widget*/
.btn-action-toolbar{
	display: flex;
  align-items: center;
  gap: 8px;
	flex-wrap: wrap;
  
}
elementor-field[disabled="disabled"] {
	background-color: #cccccc40 !important;
}

/* .select2-container{
	z-index: 10;
} */

*:not(.btn-action-toolbar) .select2-container{
	width: 100% !important;
}
.btn-action-toolbar .select2-container {
  width: 200px !important;
}

.btn-action-toolbar .button {
  padding: 8.5px 8px;
  font-size: 14px;
}
.btn-action-toolbar .button span {
  padding-left:6px;
}

.btn-action-toolbar .button i.fa-spin {
  padding:0;
}


.mysolidcam-section-license {
	display: flex !important;
	flex-direction: row !important;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	gap: 0px 10px;
	row-gap: 0px;
	column-gap: 10px;

	width: calc((1 - 1) * 100%); /* since flex-grow is 1, this equals 0 — adjust if needed */
	height: 100%;
	flex-grow: 1;
	align-self: stretch;

	border-style: dashed;
	border-width: 1px 0 1px 0;
	border-color: #CECECE;

	padding-top: 11px;
	padding-bottom: 11px;
	padding-left: 0;
	padding-right: 8px;

	transition: background 0.3s;
}


#remove-license-btn {
  border-width: 1px 1px 1px 1px;
  border-color: var( --e-global-color-b07f7d2 );
  color: var( --e-global-color-b07f7d2 );
  position:relative;
}

#remove-license-btn:hover{
  color: var( --e-global-color-dc92533 );
}


#remove-license-btn:focus {
	color: var( --e-global-color-dc92533 ) !important;

}

#toggle-add-license-btn {
  border-width: 1px 1px 1px 1px;
  border-color: #198754;
  color: #198754;
  position:relative;
}

#toggle-add-license-btn:hover{
  color: var( --e-global-color-dc92533 );
  background:#198754;
}

#toggle-add-license-btn:focus {
  color: var( --e-global-color-dc92533 );
  background: #198754;
}

.toggle-form{
	margin-top: 15px;
	display: none; 
	border: 1px solid #ddd; 
	padding: 16px; 
}
.toggle-form label {
  color: var(--e-global-color-text);
  margin-bottom: 8px;
}


.checkbox-box {
  display: flex;
  gap: 10px;
  align-items: flex-start;
	margin-top: 16px;
}

#send-license-btn,
#confirm-remove-license-btn{
  padding: 8.5px 8px;
  font-size: 14px;
  margin-top: 16px;
  background-color: var( --e-global-color-b07f7d2 );
  color: var( --e-global-color-dc92533 );
  border-color: var( --e-global-color-b07f7d2 );
}

#send-license-btn:hover,
#confirm-remove-license-btn{
  background-color: #850F0D;
}

#remove-license-confirm p {
  margin: 0;
}

/*Custom License Widget*/

/*My Dashboard*/
.card-module.on-sub,
.card-module.no-sub{
  border-left: 5px solid;
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15) !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
	min-height: 173px;
}


.card-module.on-sub{
	border-color:#198754;
}

.card-module.no-sub{
	border-color:#dee2e6;
}

.card-module.on-sub .card-module-body,
.card-module.no-sub .card-module-body{
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
}

.elementor-social-icons-wrapper {
  width: 100%;
  justify-content: space-between !important;
}


.card-module.on-sub .card-module-body{
	  color: #198754;

}

.no-licenses-box {
  display: flex;
  flex-direction: revert;
  align-items: center;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075) !important;
  padding: 16px;
  border: 1px solid #ffe69c;
  border-left: 4px solid #ffe69c;
  background: #fff3cd;
  border-radius: 8px;
  color: #664d03;
  gap: 24px;
	margin-bottom: 32px;
}

.content-box .heading {
  margin: 0px !important;
  margin-bottom: 8px !important;
}



.card-title {
  font-size: 1.25rem !important;
  font-weight: bolder !important;
  display: flex;
  justify-content: space-between;
	align-items: flex-start;
}


.card-title i{
  line-height: initial;
}

.card-module-footer{
  padding: 8px 16px;
}

.card-module-footer.bg-success.text-white .card-module-link
{
  color: #f9fafb !important;
  
}
.bg-success {
  background-color: #198754;
}

.bg-warning{
	background-color: #ffc107;
}



.text-white {
  color: #ffffff !important;
}

.add-to-subscription {
  padding: 4px 8px;
  text-align: center;
  background: #faa21b;
  color: #fff;
}
.add-to-subscription:hover{
  background-color: #fbb03d;
  border-color: #fbab32;
  color: #fff;
}

.card-module-footer.bg-white.text-dark {
  border-top: 1px solid #0000002d;
}
.card-module-footer.bg-white.text-dark .card-module-link {
  color: #000000;
}



/*Custom License Updates*/
.license-updates-widget {
	border-top: 1px solid rgba(0, 0, 0, 0.25);
	padding-top: 16px
}


.license-updates-widget h2 {
   margin:0px;
}

.license-updates-widget p {
   
}

.license-updates-widget a {
	display: inline-block;
	padding: 10px 20px;
	font-size: 16px;
	border: 1px solid;
	border-color: var( --e-global-color-text );
	color: var( --e-global-color-text );
	  transition: all .3s;
}

.license-updates-widget a i {
padding-right:8px;
}

.license-updates-widget a:hover {
  background-color: #212529;
  color: var( --e-global-color-8807009 );
	transition: all .3s;


}

/*Custom My Plans*/

.my-plan-widget {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.25);
}

.my-plan-widget h5,
.my-plan-widget h4{
  margin: 0px;
}

.my-plan-widget h5 {
  margin-bottom: 8px;
}
.text-success {
  color: #198754;
  font-weight: 700;
}

.my-plan-widget h4 {
  display: flex;
  align-items: center;
  gap: 8px;
}


.date-box {
  display: flex;
  align-items: center;
  gap: 10px;
}
.date-box span {
  font-weight: 600;
}
.progress {
  margin-top: 16px;
  border-radius: 32px;
  background: #e9ecef;
  position:relative;
}
.progress-bar span {
  display: flex;
  width: 100%;
  justify-content: center;
  font-size: 12px;
  line-height: 16px;
  color: white;
}
.progress-bar {
  border-radius: 32px;
}

.widget-my-status {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.25);
}

.status-trophies-box span i {
  font-size: 22px;
  padding-right: 4px;
}
.status-trophies-box {
  font-size: 1.5rem !important;

}
.status-table-view{
	display:none;
}

.status-table-view {
  border: 1px solid rgba(0, 0, 0, 0.25);
  margin-top: 16px;
  padding: 16px;
}

.table th,
.table td{
  border: 0px !important;
  font-weight: 700;
  text-align: left;
}


.info-icon i {
  font-size: 18px;
  vertical-align: top;
  padding-left: 4px;
}

.text-warning {
  color: #ffc107;
}


.status-trophies-box > span {
  position: relative;
}

.status-trophies-box > span .tooltip {
  top: 35px;
  left: -25px;
}
.status-trophies-box > span .tooltip:after{
  
  top: -10%;
  right: 50%;
  border-color: transparent transparent var(--e-global-color-c668341) transparent;
}


.info-icon {
  position: relative;
}
.info-icon .tooltip {
  right: -210px;
  top: 0px;
}


.progress .tooltip {
  top: 25px;
  left: 22%;
}

.progress .tooltip:after{
  
  border-color: transparent transparent var(--e-global-color-c668341) transparent;
  top: -5px;
  left: 47%;
}


/*Download Addon*/
.download-addon-widget-wrp {
  padding: 24px 16px 8px;
  border-left: 5px solid var( --e-global-color-b07f7d2 );
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15) !important;
  margin-bottom:24px;
}
.download-addon-widget-wrp .versioning-link {
  font-weight: var(--e-global-typography-2b4d2d1-font-weight);
  line-height: var(--e-global-typography-a80dbdc-line-height);
  font-size: 24px;
  color: var(--e-global-color-text);
}


.versioning-data-row {
  padding-top: 8px;
  display: flex;
  justify-content: space-between;
}
.versioning-data-row .data-left-link {
  background-color: #212529 !important;
  display: inline-block;
  padding: 4px 8px;
  border: 2px solid #212529;
  color: var(--e-global-color-dc92533);
  font-size: var(--e-global-typography-b98f8f6-font-size);
  line-height: var(--e-global-typography-a80dbdc-line-height);
  font-weight: var(--e-global-typography-primary-font-weight);
}
.sub-heading {
  margin: 16px 0px 0px;
}

.data-link-right > i {
  color:#fff
	
}
.data-link-right > span {
  width: 60px;
  height: 60px;
  text-align: center;
  border-radius: 50%;
  color: var(--e-global-color-dc92533);
  background: var( --e-global-color-b07f7d2 );
  display: flex;
  align-items: center;
  justify-content: center;
}

/* SolidCAM DL */
.download-addon-widget-wrp.module-add-on {
  border-color: var( --e-global-color-ab4ef0a );
}
.module-add-on .data-link-right > span {
  color: var(--e-global-color-dc92533); /*weiss*/
  background: var( --e-global-color-ab4ef0a );
}
/* SolidCAM MAKER DL */
.download-addon-widget-wrp.module-scmv {
  border-color: var( --e-global-color-ab4ef0a );
}
.module-scmv .data-link-right > span {
  color: var(--e-global-color-dc92533); /*weiss*/
  background: var( --e-global-color-ab4ef0a );
}
/* InventorCAM DL */
.download-addon-widget-wrp.module-ivcam {
  border-color: var( --e-global-color-63e275f );
}
.module-ivcam .data-link-right > span {
  color: var(--e-global-color-dc92533); /*weiss*/
  background: var( --e-global-color-63e275f );
}
/* InventorCAM MAKER DL */
.download-addon-widget-wrp.module-icmv {
  border-color: var( --e-global-color-63e275f );
}
.module-icmv .data-link-right > span {
  color: var(--e-global-color-dc92533); /*weiss*/
  background: var( --e-global-color-63e275f );
}
/* SolidCAM for Solid Edge DL */
.download-addon-widget-wrp.module-scse {
  border-color: var( --e-global-color-74131e9 );
}
.module-scse .data-link-right > span {
  color: var(--e-global-color-dc92533); /*weiss*/
  background: var( --e-global-color-74131e9 );
}
/* SolidCAM CADCAM DL */
.download-addon-widget-wrp.module-cad-cam {
  border-color: var( --e-global-color-d32178c );
}
.module-cad-cam .data-link-right > span {
  color: var(--e-global-color-dc92533); /*weiss*/
  background: var( --e-global-color-d32178c );
}

.version-release-date-wrp {
  display: flex;
  gap: 16px;
  padding-top: 8px;
}
.release-notes {
  display: flex;
  gap: 16px;
}
.release-notes > a {
  display: inline-block;
  color: #000;
}
.release-notes > a > i {
  padding-right: 8px;
}


.bottom-btns-wrp {
  display: flex;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.25);
  gap: 8px;
	flex-wrap: wrap;
}

.bottom-btns-wrp .button-wrp button,
.bottom-btns-wrp .button-wrp.last-btn
{
  padding: 4px 8px;
  font-size: 14px;
  line-height: 20px;
  position:relative;
}

.bottom-btns-wrp .button-wrp.last-btn > a
{
  color: var(--e-global-color-text);
}

.bottom-btns-wrp .button-wrp button:after{
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent; 
}



.bottom-btns-wrp .button-wrp.last-btn{
	border:1px solid #212529;
}


.button-wrp {
  position: relative;
}


.dropdown-menu {
 list-style: none;
  border: 1px solid #0000002d;
  border-radius: 12px;
  padding: 0px;
  overflow: hidden;
  position: absolute;
  background: #fff;
  z-index: 10;
  min-width: 170px;
  top: 35px;
  display:none;
}
.dropdown-item.active {
  color: var(--e-global-color-dc92533);
  text-decoration: none;
  background-color: #555 !important;
}
.dropdown-item {
  padding: 4px 16px;
  width: 100%;
  display: inline-block;
  color: #000000;
}



.dropdown-menu > li:hover .dropdown-item {
	background-color: #555;
	color: var(--e-global-color-dc92533);
  
}

#remove-license-btn .tooltip {
  top: -60px;
  width: 200px;
  right: unset;
  left: -50px;
  overflow-wrap: break-word;
  white-space: normal;
}

#remove-license-btn .tooltip:after{
  border-color: var(--e-global-color-c668341) transparent transparent transparent;
  top: unset;
  bottom: -10px;
  left: 45%;
}

#toggle-add-license-btn .tooltip {
  top: -38px;
  width: auto;
  right: unset;
  left: -65px;
}

#toggle-add-license-btn .tooltip:after{
  border-color: var(--e-global-color-c668341) transparent transparent transparent;
  top: unset;
  bottom: -10px;
  left: 45%;
}


.add-to-subscription.tooltip-container {
  position: relative;
}
.add-to-subscription .tooltip {
  top: -35px;
  left: 60px;
}

.add-to-subscription .tooltip:after{
  border-color: var(--e-global-color-c668341) transparent transparent transparent;
  top: unset;
  bottom: -10px;
  left: 45%;
}


.disabled {
  filter: grayscale(100%);
  opacity: 0.6;
	pointer-events: none;
  cursor: default;
  text-decoration: none;
}


.user-data-wrp {
  display: none;
}

.user-data-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--e-global-color-text);
}

.user-data-link:hover {
  color: var(--e-global-color-text);
}

.user-data-link:hover {
  color: var(--e-global-color-text);
}

.user-name-heading:hover{
	cursor:pointer;
}

.user-name-heading  h2::after {
  display: inline-block;
  margin-left: 0.5em;
  vertical-align: 0.2em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.user-data-link .user-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}
.user-name {
  color: var(--e-global-color-text);
}

.userdata-dropdown {
  list-style: none;
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15) !important;
  padding: 8px 0px;
  border: 1px solid #DEE2E6;
  border-radius: 8px;
  position: absolute;
  z-index: 100;
  background: #fff;
}

.user-plans {
  margin: 0px;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}
.user-plans > span {
  color: #fff !important;
  background-color: #198754 !important;
  border-radius: 100px;
  font-size: 10.5px;
  padding: 3.6px 6.8px;
  margin-left:8px;
}


.user-data-wrp .elementor-icon-list-item {
  padding: 0px 16px !important;
}
.user-data-wrp .elementor-icon-list-item:nth-child(2) {
  border-bottom: 1px solid #0000002d;
  padding-bottom: 16px !important;
  margin-bottom: 8px !important;
  margin-top: 8px !important;
}
.user-data-wrp .elementor-icon-list-item:last-child {
 border-top: 1px solid #0000002d;
  padding-top: 8px !important;
  margin-top: 8px !important;
  display: flex;
  gap: 8px;
}

.user-data-wrp .elementor-icon-list-item:last-child .elementor-icon-list-icon {
  order: 2;
}
.user-data-wrp .elementor-icon-list-item:last-child .elementor-icon-list-text {
  padding: 0px;
}

.user-data-wrp .elementor-icon-list-item:last-child .elementor-icon-list-text > a{
  color: var( --e-global-color-text );
}

.user-data-wrp .elementor-icon-list-item:hover{
	background:#f8f9fa;
}


.user-profile-wrp {
 display: flex;
  justify-content: space-between;
  padding: 24px 16px 24px;
  border-left: 5px solid var( --e-global-color-b07f7d2 );
  /*box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15) !important;*/
  margin-bottom: 24px;
}
.user-profile-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.post-total-count-wrp {
  display: flex;
  gap: 16px;
}

.post-total-count {
  margin-left: auto;
}

#user-profile-widget {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.25);
}

.edit-profile-btn {
  font-weight: 400;
  color: var( --e-global-color-text );
  border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: var( --e-global-color-text );
  border-radius: 0px 0px 0px 0px;
  align-self: flex-start;
  padding: 4px 8px;
  font-size: 14px;
  line-height: 20px;
  position: relative;
	transition: all .3s;
}

.profile-email {
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-profile-btn:hover {
	background-color: var( --e-global-color-primary );
	color: #FFFFFF;
	border-style: solid;
	border-color: var( --e-global-color-primary );
	transition: all .3s;
}

.user-name-heading {
  margin: 0px;
}

.user-email {
  margin: 0;
}

.profile-name {
  display: flex;
  gap: 4px;
  align-items: center;
}

/*My Dashboard Ends*/


/*Password Reset Page*/
 .um-um_password_id.um {
  max-width: 550px !important;
  padding:48px 0px;
  margin:auto;
}
body .um.um-password:not(.um-admin),
body .um-field-block{
	  color: var(--e-global-color-text) !important;
	  margin-bottom:0px !important;
	  
}
body .um.um-password .um-form input:autofill{
	-webkit-text-fill-color: var(--e-global-color-text) !important;
}


body .um.um-password .um-form .um-col-alt{
	  margin: 0px 0px 0px 0px !important;
}

body .um.um-password .um-form .um-field-block{
	padding:0px;
}


body .um.um-password .um-form input[type="text"],
body .um .um-form input[type="text"],
body .um .um-form input[type="password"],
body .login-form-shortcode .um .um-form input[type="text"],
body .login-form-shortcode .um .um-form input[type="password"]{
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  font-weight: 400 !important;
  border: 1px solid var(--e-global-color-0fc47c7) !important;
  border-radius: 0 !important;
  line-height: 1.4;
}

body .um.um-password input[type="submit"].um-button,
body .um input[type="submit"].um-button{
	color: var( --e-global-color-c668341 ) !important;
  font-size: var( --e-global-typography-48312dc-font-size ) !important; 
  line-height: var( --e-global-typography-48312dc-line-height ) !important;
  background-color: #02010100 !important;
  font-weight: 400;
  color: var( --e-global-color-text ) !important;
  border-style: solid !important;
  border-width: 1px 1px 1px 1px !important;
  border-color: var( --e-global-color-text ) !important;
  border-radius: 0px 0px 0px 0px !important;
  padding: 15px 15px 15px 15px !important;
}

body .um.um-password input[type="submit"].um-button:hover,
body .um input[type="submit"].um-button:hover{

background-color: #212529 !important;
  color: var( --e-global-color-8807009 ) !important;

}

body a.um-link-alt {
  color: var( --e-global-color-b07f7d2 ) !important;
}

a.um-link-alt:hover{
  color: #850F0D !important;
  text-decoration: none !important;
}

.elementor-message-success {
  background-color: #CDEACA;
  border: 1px solid #58B548;
  color: #3B7826;
  width: 100%;
  padding: 12px 12px 12px 36px;
  font-size: 16px !important;
} 


 body .um-login.um-logout,
body .um-login{
  width: 550px !important;
  margin: auto;
  max-width: unset !important;
  padding: 48px 0px;
}
body .um-login.um-logout .um-misc-ul {
  list-style: none;
  padding-left: 0px;
  text-align: center;
}

body .um-login.um-logout .um-misc-ul > li {
margin-bottom: 8px
}

body .um-login.um-logout .um-misc-ul > li > a {
color: var( --e-global-color-text );
  transition: color 0.3s;
  text-decoration: none;

font-family: var( --e-global-typography-48312dc-font-family ), Sans-serif;
font-size: var( --e-global-typography-48312dc-font-size );
line-height: var( --e-global-typography-48312dc-line-height );
}



body .um-login.um-logout .um-misc-with-img {
  border-bottom: 1px solid #0000002d;
}


body .um-login.um-logout strong {
  font-weight: normal;
  color: var( --e-global-color-text );

} 

.login-form-shortcode .um.um-login {
  padding: 0px;
  width: auto !important;
  margin: 0px !important;
}

.login-form-shortcode .um-field-username{
	padding-top:0px;
}


body .login-form-shortcode .um-field-label label{
	font-size:16px !important;
}


.reseller-download-widget .download-addon-widget-wrp {
  margin: 0px !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.reseller-download-widget {
  height: 100%;
}


#send-request .elementor-button-content-wrapper {
  align-items: center;
}



.custom-certificates-cta .elementor-cta {
  gap: 48px;
}


.custom-certificates-cta .elementor-cta  .elementor-cta__bg-wrapper {
  height: 134px;
}



/*SC OFFICE POST GRID*/
.sc_office_post_grid .uc_post_title {
  border-bottom: 1px solid #adb5bd;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.office-post-grid-content {
  display: flex;
  gap: 24px;
  flex-direction:row;

}

.office-post-grid-content .uc_content-info-wrapper {
  display: flex;
  gap: 16px;
  width: 70%;
  flex-direction:column;

}

.phones {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
}
.phones a {
  color: var(--e-global-color-text);
  display: flex;
  gap: 8px;
}

.upper-content {
  display: flex;
  gap:16px;
}

.office-post-grid-content .uc_content-info-wrapper .left-content,
.office-post-grid-content .uc_content-info-wrapper .right-content{
  flex: 1;
}

.equipment-list {
  list-style: none;
  padding: 0px;
}

.equipment-list li {
  color: #198754 !important;
  border-top: 1px solid #dee2e6 ;
  padding: 4px 8px 4px 16px;
}
.management-wrp {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 16px;
}
.management-wrp .user-img {
  width: 100px;
  height: 100px;
}
.management-wrp .user-img > img {
  border-radius: 100%;
}
.contact-links {
  margin-top: 16px;
  display: flex;
  flex-direction: row;
  gap: 8px;
  text-align: center;
}
.contact-links > a {
  border: 1px solid #212529;
  display: inline-block;
  padding: 6px 12px;
  color: #212529;
  margin-right: 8px;
  transition: 0.3 all;
}


.contact-links > a:hover {
  background:#212529;
  color: #fff;
  margin-right: 8px;
	  transition: all 0.8s ease;

}

.contact-links > a > i {
  padding-left: 10px;
 
}

.documentation-wrp .ue_taxonomy_item_row.child-category:nth-child(1) {
  order: 2;
} 
.documentation-wrp .ue_taxonomy_item_row.child-category:nth-child(2) {
  order: 1;
}
.documentation-wrp .ue_taxonomy_item_row.child-category:nth-child(3) {
  order: 3;
}


/*Reseller Network Images*/
.custom-country-list .ue_taxonomy_item .ue_taxonomy_image {
  height: 300px;
  background-repeat: no-repeat !important;
  background-position:center !important;
  background-size: cover !important;
}

/*For Americas*/
/*.ue_taxonomy_item.Americas .ue_taxonomy_image{*/
/*      background: url(/wp-content/uploads/2024/09/americas.svg);*/
/*}*/
/*.ue_taxonomy_item.Americas .ue_taxonomy_image.selected{*/
/*    background: url(/wp-content/uploads/2024/09/americas-active.svg);*/
/*}*/

/*For Africa*/
/*.ue_taxonomy_item.Africa .ue_taxonomy_image{*/
/*      background: url(/wp-content/uploads/2024/09/africa.svg);*/
/*}*/
/*.ue_taxonomy_item.Africa .ue_taxonomy_image.selected{*/
/*    background: url(/wp-content/uploads/2024/09/africa-active.svg);*/
/*}*/

/*For Asia*/
/*.ue_taxonomy_item.Asia .ue_taxonomy_image{*/
/*      background: url(/wp-content/uploads/2024/09/asia-pacific.svg);*/
/*}*/
/*.ue_taxonomy_item.Asia .ue_taxonomy_image.selected{*/
/*    background: url(/wp-content/uploads/2024/09/asia-pacific-active.svg);*/
/*}*/

/*For Germany*/
/*.ue_taxonomy_item.Germany .ue_taxonomy_image{*/
/*      background: url(/wp-content/uploads/2024/09/germany.svg);*/
/*}*/
/*.ue_taxonomy_item.Germany .ue_taxonomy_image.selected{*/
/*    background: url(/wp-content/uploads/2024/09/germany-active.svg);*/
/*}*/

/*For Europe*/
/*.ue_taxonomy_item.Europe .ue_taxonomy_image{*/
/*      background: url(/wp-content/uploads/2024/09/europe.svg);*/
/*}*/
/*.ue_taxonomy_item.Europe .ue_taxonomy_image.selected{*/
/*    background: url(/wp-content/uploads/2024/09/europe-active.svg);*/
/*}*/












@media (max-width:992px){
	.office-post-grid-content {
	  flex-direction:column;
	}
	.office-post-grid-content .uc_content-info-wrapper{
		width:100%;
	}
	
}

@media (min-width:992px) and (max-width:1024px){
	.grid-column-span {
	  grid-column: span 2;
	}
}

@media (min-width: 768px) {
  .Technology-partners-wrp
	.elementor-image-gallery
	.gallery-columns-4
	.gallery-item {
	max-width: 33%;
  }

  .ue_post_accordion_content_in {
	grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1300px) {
  .Technology-partners-wrp
	.elementor-image-gallery
	.gallery-columns-4
	.gallery-item {
	max-width: 25%;
  }

  .ue_post_accordion_content_in {
	grid-template-columns: repeat(3, 1fr);
  }

  .custom-testimonials-shadow .elementor-main-swiper.swiper {
	padding: 40px 40px;
  }

  body .custom-width {
	width: 33.33333333%;
  }
}

/*@media (min-width: 1400px) {*/
/*  .custom-testimonials-shadow .elementor-swiper-button {*/
/*    display: none !important;*/
/*  }*/
/*}*/

@media (min-width: 1600px) {
  .ue_post_accordion_content_in {
	grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  body .inline-btn-show {
	width: 100vw;
	z-index: 10;
  }
  .current-language-flag .language-button {
	font-size: 12px;
	line-height: 18px;
  }

  .bg-white {
	background: var(--e-global-color-dc92533) !important;
  }
  .custom-background-color .elementor-background-video-container {
	display: none;
  }
  .custom-background-color {
	background: #212529;
  }

  .ue-custom-taxonomies-box {
	width: 100%;
  }
  .ue_taxonomy {
	flex-wrap: wrap;
  }
  .bg-white.elementor-section--handles-inside {
	background-color: #fff;
  }

  body .custom-testimonials-shadow .swiper-slide {
	padding: 16px !important;
  }

  .icon-box-list-style .elementor-icon-box-icon {
	display: inline-flex !important;
	flex: 0 0 auto !important;
  }
}



@media (max-width:500px){
	.user-profile-wrp{
		flex-direction: column;
		gap: 24px;
	}
	 .contact-links{
		flex-direction: column;
	}
}


@media (min-width:768px) and (max-width:875px){
	.user-profile-wrp{
		flex-direction: column;
		gap: 24px;
	}
	 .contact-links{
		flex-direction: column;
	}
}



@media (min-width:1024px) and (max-width:1400px){
	.user-profile-wrp{
		flex-direction: column;
		gap: 24px;
	}
}


/* Website Fixes 26-06-2025 */

.current-language-flag img.language-flag{width:18px;height:18px;object-fit:contain;}

/* body .mega-menu-dropdown .elementor-nav-menu .elementor-nav-menu--dropdown.visible{right:0;opacity:1;}
body .mega-menu-dropdown .elementor-nav-menu .elementor-nav-menu--dropdown{transition:0.5s all;} */


@media (max-width:1300px){
  .box-shadow,.bg-white {padding-left: 20px !important;padding-right: 20px !important;}
  
}

@media (max-width:1250px){
.main-menu-horizontal .elementor-nav-menu li a{margin:0 8px !important;}

}

@media (max-width:1199px){

.main-menu-horizontal .elementor-nav-menu li a{font-size:14px !important;}
.language-popup-styling ul > li{width:calc(100%/3 - 0px);}
.cust_rev_txt p{font-size:14px;}
.cust_rev_stars .e-rating .e-icon-wrapper svg{height:16px !important;}

}

@media (max-width:767px){
  .language-popup-styling ul > li{width:calc(100%/2 - 0px);}
}


@media (max-width:480px){
.wpml-ls-legacy-list-horizontal{padding:0 !important;}
div#language-switcher-wrp{padding:13px !important;}
.wpml-ls-legacy-list-horizontal .wpml-ls-flag+span{font-size:15px !important;}
.box-shadow,.bg-white{padding-left:15px !important;padding-right:15px !important;}
}

/* Hide on tablet and desktop */
@media only screen and (min-width: 768px) {
  .e-con-inner > .elementor-widget-nav-menu > .elementor-widget-container > .elementor-nav-menu--main > ul.elementor-nav-menu > li:last-child {
	display: none;
  }
}

/* Show on mobile */
@media only screen and (max-width: 767px) {
  .e-con-inner > .elementor-widget-nav-menu > .elementor-widget-container > .elementor-nav-menu--main > ul.elementor-nav-menu > li:last-child {
	display: block;
  }
}

.test_cls{
	transition: 0.3s all;
}
 
.test_cls:hover .elementor-icon-box-icon {}
 
.test_cls:hover .elementor-icon-box-icon span.elementor-icon {
  transform: scale(1.11);
  transition: 0.3s all;
}

/* License Widget Styling - Proper Widget Classes */
/* License widget container */
.custom-license-widget {
	display: flex;
	flex-direction: column;
	gap: 0px;
}

/* License widget header */
.custom-license-widget .elementor-heading-title {
	font-weight: 600;
	color: var( --e-global-color-text );
	margin: 0px 0px 20px 0px;
}

/* License row container */
.custom-license-widget .license-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: 10px;
	flex-wrap: nowrap;
	background-transition: 0.3s;
	border-style: dashed;
	border-width: 1px 0px 0px 0px;
	border-color: #CECECE;
	padding: 11px 8px 11px 0px;
}

.custom-license-widget .license-row:hover {
	background-color: #F5F5F5;
}

/* License icon styling */
.custom-license-widget .license-icon .elementor-widget-container {
	margin: 0px 0px -5px 0px;
}

.custom-license-widget .license-icon .elementor-icon {
	font-size: 20px;
}

.custom-license-widget .license-icon .elementor-icon svg {
	height: 20px;
	width: auto;
}

/* Active license (green key) */
.custom-license-widget .license-row:not(.license-locked) .elementor-icon svg {
	fill: var( --e-global-color-056fbbf );
}

/* License number styling */
.custom-license-widget .license-number .elementor-heading-title {
	font-family: "din-2014-narrow", Sans-serif;
	font-size: 23px;
	color: var( --e-global-color-056fbbf );
	margin: 0;
}

/* Info icon styling */
.custom-license-widget .license-info .elementor-widget-container {
	margin: 0px 0px -5px 0px;
}

.custom-license-widget .license-info .elementor-icon {
	font-size: 25px;
}

.custom-license-widget .license-info .elementor-icon svg {
	height: 25px;
	width: auto;
	fill: #91BD0866;
}

.custom-license-widget .license-info .elementor-icon:hover svg {
	fill: var( --e-global-color-056fbbf );
}

/* Spacer styling */
.custom-license-widget .license-spacer {
	flex: 1;
	min-width: 15px;
}

/* Delete button styling (for active license) */
.custom-license-widget .license-delete .elementor-button {
	background-color: #91BD0800;
	font-family: "din-2014-narrow", Sans-serif;
	font-size: 16px;
	font-weight: 400;
	text-transform: uppercase;
	fill: var( --e-global-color-e0f946a );
	color: var( --e-global-color-e0f946a );
	border-style: solid;
	border-width: 2px;
	border-color: var( --e-global-color-e0f946a );
	border-radius: 50px;
	padding: 3px;
	text-decoration: none;
}

.custom-license-widget .license-delete .elementor-button:hover, 
.custom-license-widget .license-delete .elementor-button:focus {
	background-color: var( --e-global-color-e0f946a );
	color: var( --e-global-color-dc92533 );
	border-color: var( --e-global-color-e0f946a );
}

.custom-license-widget .license-delete .elementor-button:hover svg, 
.custom-license-widget .license-delete .elementor-button:focus svg {
	fill: var( --e-global-color-dc92533 );
}

/* Action button styling */
.custom-license-widget .license-action {
	flex-grow: 1;
	flex-shrink: 0;
	text-align: right;
}

.custom-license-widget .license-action .elementor-button {
	background-color: #91BD0800;
	font-family: "din-2014-narrow", Sans-serif;
	font-size: 20px;
	font-weight: 600;
	text-transform: uppercase;
	border-style: solid;
	border-width: 2px;
	border-radius: 50px;
	padding: 5px 15px 5px 11px;
	text-decoration: none;
	display: inline-flex;
	align-items: center;
	gap: 7px;
}

/* Selected license styling - green icon, title, and button */
.custom-license-widget .license-row[data-type="selected"] .license-icon .elementor-icon svg {
	fill: var( --e-global-color-056fbbf );
}

.custom-license-widget .license-row[data-type="selected"] .license-number .elementor-heading-title {
	color: var( --e-global-color-056fbbf );
}

.custom-license-widget .license-row[data-type="selected"] .license-action .elementor-button {
	fill: var( --e-global-color-056fbbf );
	color: var( --e-global-color-056fbbf );
	border-color: transparent;
	padding: 5px 0px;
}

.custom-license-widget .license-row[data-type="selected"] .license-action .elementor-button:hover,
.custom-license-widget .license-row[data-type="selected"] .license-action .elementor-button:focus {
	color: #91BD08;
	border-color: transparent;
	background-color: transparent;
}

.custom-license-widget .license-row[data-type="selected"] .license-action .elementor-button:hover svg,
.custom-license-widget .license-row[data-type="selected"] .license-action .elementor-button:focus svg {
	fill: #91BD08;
}

/* Locked license styling */
.custom-license-widget .license-locked .elementor-icon svg {
	fill: var( --e-global-color-e0f946a );
}

.custom-license-widget .license-locked .license-number .elementor-heading-title {
	color: var( --e-global-color-e0f946a );
}

.custom-license-widget .license-locked .license-action .elementor-button {
	fill: var( --e-global-color-e0f946a );
	color: var( --e-global-color-e0f946a );
	border-color: var( --e-global-color-e0f946a );
	cursor: pointer;
}

.custom-license-widget .license-locked .license-action .elementor-button:hover,
.custom-license-widget .license-locked .license-action .elementor-button:focus {
	background-color: var( --e-global-color-056fbbf );
	color: var( --e-global-color-dc92533 );
	border-color: var( --e-global-color-056fbbf );
}

.custom-license-widget .license-locked .license-action .elementor-button:hover svg,
.custom-license-widget .license-locked .license-action .elementor-button:focus svg {
	fill: var( --e-global-color-dc92533 );
}

/* License Pagination Styling - Minimal with License Color Scheme */
.custom-license-widget .license-pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 6px;
	margin: 15px 0 10px 0;
	padding: 8px 0;
}

.custom-license-widget .pagination-btn {
	padding: 6px 10px;
	border: 1px solid var(--e-global-color-e0f946a);
	background: transparent;
	color: var(--e-global-color-e0f946a);
	cursor: pointer;
	border-radius: 20px;
	font-size: 12px;
	font-weight: 500;
	transition: all 0.3s ease;
	min-width: 28px;
	height: 28px;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
}

.custom-license-widget .pagination-btn:hover {
	background: #555555;
	border-color: #555555;
	color: white;
}

.custom-license-widget .pagination-btn.active {
	background: var(--e-global-color-056fbbf);
	color: white;
	border-color: var(--e-global-color-056fbbf);
}

.custom-license-widget .pagination-btn.active:hover {
	background: #91BD08;
	border-color: #91BD08;
}

/* Available license styling (different from expired) - but NOT selected */
.custom-license-widget .license-row:not(.license-expired):not([data-type="selected"]) .license-number .elementor-heading-title {
	color: var( --e-global-color-secondary );
}

.custom-license-widget .license-row:not(.license-expired):not([data-type="selected"]) .elementor-icon svg {
	fill: var( --e-global-color-secondary );
}

/* Available license SELECT button styling - gray by default, green on hover */
.custom-license-widget .license-row[data-type="available"] .license-action .elementor-button {
	color: #555555;
	border-color: #555555;
	background-color: transparent;
	cursor: pointer;
}

.custom-license-widget .license-row[data-type="available"] .license-action .elementor-button svg {
	fill: #555555;
}

.custom-license-widget .license-row[data-type="available"] .license-action .elementor-button:hover,
.custom-license-widget .license-row[data-type="available"] .license-action .elementor-button:focus {
	background-color: var( --e-global-color-056fbbf );
	color: var( --e-global-color-dc92533 );
	border-color: var( --e-global-color-056fbbf );
}

.custom-license-widget .license-row[data-type="available"] .license-action .elementor-button:hover svg,
.custom-license-widget .license-row[data-type="available"] .license-action .elementor-button:focus svg {
	fill: var( --e-global-color-dc92533 );
}

/* Expired license styling */
.custom-license-widget .license-expired .license-icon .elementor-icon svg {
	fill: var( --e-global-color-e0f946a );
}

.custom-license-widget .license-expired .license-number .elementor-heading-title {
	color: var( --e-global-color-e0f946a );
}

.custom-license-widget .license-expired .license-info .elementor-icon svg {
	fill: var( --e-global-color-e0f946a ) !important;
}

.custom-license-widget .license-expired .license-action .expired-license-btn {
	fill: var( --e-global-color-e0f946a );
	color: var( --e-global-color-e0f946a );
	border-color: var( --e-global-color-e0f946a );
	cursor: not-allowed;
	opacity: 0.6;
	pointer-events: none;
}

.custom-license-widget .license-expired .license-action .expired-license-btn:hover,
.custom-license-widget .license-expired .license-action .expired-license-btn:focus {
	background-color: transparent;
	color: var( --e-global-color-e0f946a );
	border-color: var( --e-global-color-e0f946a );
}

/* Info icon opacity styling */
.custom-license-widget .license-info {
	opacity: 0.4;
	transition: opacity 0.3s ease;
}

.custom-license-widget .license-info:hover {
	opacity: 1;
}


/* Loading state for buttons */
.custom-license-widget .elementor-button:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.custom-license-widget .elementor-button .fa-spinner {
	animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Tooltip styling for mysc-tooltip */
.mysc-tooltip {
	position: relative;
	display: inline-block;
}

.mysc-tooltip:hover::after {
	content: attr(data-tooltip);
	position: absolute;
	bottom: 125%;
	left: 50%;
	transform: translateX(-50%);
	background-color: #333;
	color: white;
	padding: 8px 12px;
	border-radius: 4px;
	font-size: 12px;
	white-space: pre-line;
	z-index: 1000;
	opacity: 1;
	visibility: visible;
	transition: opacity 0.3s;
	max-width: 250px;
	width: max-content;
	text-align: center;
	box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.mysc-tooltip:hover::before {
	content: "";
	position: absolute;
	bottom: 120%;
	left: 50%;
	transform: translateX(-50%);
	border: 5px solid transparent;
	border-top-color: #333;
	z-index: 1000;
}

.mysc-tooltip::after,
.mysc-tooltip::before {
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.3s, visibility 0.3s;
}

/* Unlocked license SELECT button styling - gray by default, green on hover */
.custom-license-widget .license-row[data-type="unlocked"] .license-action .elementor-button {
	fill: #555555;
	color: #555555;
	border-color: #555555;
	background-color: transparent;
	cursor: pointer;
}

.custom-license-widget .license-row[data-type="unlocked"] .license-action .elementor-button:hover,
.custom-license-widget .license-row[data-type="unlocked"] .license-action .elementor-button:focus {
	background-color: var( --e-global-color-056fbbf );
	color: var( --e-global-color-dc92533 );
	border-color: var( --e-global-color-056fbbf );
}

.custom-license-widget .license-row[data-type="unlocked"] .license-action .elementor-button:hover svg,
.custom-license-widget .license-row[data-type="unlocked"] .license-action .elementor-button:focus svg {
	fill: var( --e-global-color-dc92533 );
}

.new-draft .download-addon-widget-wrp{
	box-shadow: none !important;
}

/* License Search Field Styles */
.custom-license-widget .license-header {
	margin-bottom: 20px;
	display: flex;
	align-items: center;
	gap: 20px;
	flex-wrap: wrap;
	justify-content: space-between;
}

.custom-license-widget .license-search-container {
	position: relative;
	flex: 1;
	min-width: 200px;
	max-width: 400px;
}

.custom-license-widget .license-header h4 {
	margin: 0;
	flex-shrink: 0;
}

.custom-license-widget .license-search-input {
	width: 100%;
	padding: 8px 35px 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
	transition: border-color 0.3s ease;
}

.custom-license-widget .license-search-input:focus {
	outline: none;
	border-color: #ddd;
  box-shadow: none;
}

.custom-license-widget .license-search-icon,
.custom-license-widget .license-clear-icon {
	position: absolute;
	right: 10px;
	top: 50%;
	transform: translateY(-50%);
	color: #999;
	cursor: pointer;
	transition: color 0.3s ease;
}

.custom-license-widget .license-search-icon {
	pointer-events: none;
}

.custom-license-widget .license-clear-icon:hover {
	color: #666;
}

@media (max-width: 768px) {
	.custom-license-widget .license-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 15px;
	}
	
	.custom-license-widget .license-search-container {
		max-width: 100%;
		min-width: auto;
	}
}