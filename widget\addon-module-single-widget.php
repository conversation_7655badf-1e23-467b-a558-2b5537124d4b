<?php
if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

class Single_Addon_Module_Widget extends \Elementor\Widget_Base {

	public function get_name() {
		return 'single_addon_module';
	}

	public function get_title() {
		return __('MySolidCAM Add-on', 'solidcam');
	}

	public function get_icon() {
		return 'eicon-folder';
	}

	public function get_categories() {
		return ['general'];
	}

	protected function _register_controls() {
		$this->start_controls_section(
			'content_section',
			[
				'label' => __('Module Settings', 'solidcam'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
			]
		);
		
		$this->add_control(
			'module_key',
			[
				'label' => __('Select Module', 'solidcam'),
				'type' => \Elementor\Controls_Manager::SELECT,
				'options' => [
					'iMachining 2D' => __('iMachining 2D', 'solidcam'),
					'iMachining 3D' => __('iMachining 3D', 'solidcam'),
					'2.5D Milling' => __('2.5D Milling', 'solidcam'),
					'AFRM' => __('AFRM', 'solidcam'),
					'HSS High-Speed Surfacing' => __('HSS High-Speed Surfacing', 'solidcam'),
					'Indexial 5-Axis Milling' => __('Indexial 5-Axis Milling', 'solidcam'),
					'THSR & THSM' => __('THSR & THSM', 'solidcam'),
					'Turning' => __('Turning', 'solidcam'),
					'Simultaneous 5-Axis Milling' => __('Simultaneous 5-Axis Milling', 'solidcam'),
					'Mill-Turn Swiss-Type' => __('Mill-Turn Swiss-Type', 'solidcam'),
					'Solid Probe' => __('Solid Probe', 'solidcam'),
				],
				'default' => 'iMachining 2D',
			]
		);

		$this->add_control(
			'title',
			[
				'label' => __('Title', 'solidcam'),
				'type' => \Elementor\Controls_Manager::TEXT,
				'default' => __('Add-on Title', 'solidcam'),
				'placeholder' => __('Enter the title of the module', 'solidcam'),
			]
		);

		$this->add_control(
			'description',
			[
				'label' => __('No active Text', 'solidcam'),
				'type' => \Elementor\Controls_Manager::TEXTAREA,
				'default' => __('Add-on Description', 'solidcam'),
				'placeholder' => __('Enter the text when module is not active', 'solidcam'),
			]
		);

		$this->add_control(
			'active_text',
			[
				'label' => __('Active Text', 'solidcam'),
				'type' => \Elementor\Controls_Manager::TEXTAREA,
				'default' => __('Activated', 'solidcam'),
				'placeholder' => __('Enter the text when module is active', 'solidcam'),
			]
		);

		$this->add_control(
			'link',
			[
				'label' => __('More Info Link', 'solidcam'),
				'type' => \Elementor\Controls_Manager::URL,
				'placeholder' => __('https://your-link.com', 'solidcam'),
			]
		);

		$this->end_controls_section();
	}

	protected function render() {
		$settings = $this->get_settings_for_display();
	
		// Get widget settings
		$title = !empty($settings['title']) ? $settings['title'] : __('Untitled', 'solidcam');
		$description = !empty($settings['description']) ? $settings['description'] : __('No description available.', 'solidcam');
		$active_text = !empty($settings['active_text']) ? $settings['active_text'] : __('Activated', 'solidcam');
		$link = isset($settings['link']) ? $settings['link'] : ['url' => '#'];
		$module_key = !empty($settings['module_key']) ? $settings['module_key'] : null;
	
		if (!$module_key) {
			echo '<p>' . esc_html__('Module key is missing.', 'solidcam') . '</p>';
			return;
		}
	
		// Get current user
		$current_user = wp_get_current_user();
		if (!$current_user->exists()) {
			echo '<p>' . esc_html__('Please log in to view the module.', 'solidcam') . '</p>';
			return;
		}
	
		$user_id = $current_user->ID;
	
		// Fetch user metadata
		$active_license = get_user_meta($user_id, 'active_user_license', true);
		$customer_modules = get_user_meta($user_id, 'customer_modules', true);
		
		$customer_account = get_user_meta($user_id, 'customer_account', true);
		$customer_on_sub = isset($customer_account['customer_on_sub']) ? (bool)$customer_account['customer_on_sub'] : false;
	
		// Validate customer modules
		if ( !$customer_on_sub ) {
			echo '<p>' . esc_html__('No subscription found.', 'solidcam') . '</p>';
			return;
		}
		
		// Validate customer modules
		if (empty($customer_modules) || !is_array($customer_modules)) {
			echo '<p>' . esc_html__('No modules available for the current user.', 'solidcam') . '</p>';
			return;
		}
	
		// Find matching license data
		$matched_array = array_filter($customer_modules, function ($sub_array) use ($active_license) {
			return isset($sub_array['license_number']) && $sub_array['license_number'] == $active_license;
		});
	
		$matched_license_data = reset($matched_array);
	
		// Check if the module is active
		$is_active = isset($matched_license_data[$module_key]) && $matched_license_data[$module_key] == 'true';
		$card_class = $is_active ? 'on-sub' : 'no-sub';
		$icon_class = $is_active ? 'fas fa-shield-check text-success' : 'fal fa-exclamation-circle text-danger';
		$footer_class = $is_active ? 'bg-success text-white' : 'bg-white text-dark';
		$display_text = $is_active ? $active_text : $description;
		
		// Render the module card
		echo '<div class="card-module ' . esc_attr($card_class) . '" data-key="' . $module_key . '">';
		echo '    <div class="card-module-body">';
		echo '        <div class="card-title fs-5 fw-bolder">';
		echo            esc_html($title);
		echo '            <i class="fa-lg ' . esc_attr($icon_class) . ' float-end" aria-hidden="true"></i>';
		echo '        </div>';
		echo '        <p class="card-text">' . esc_html($display_text) . '</p>';
		if (!$is_active) {
			echo '        <a class="add-to-subscription" href="javascript:void(0)" data-title="' . esc_html__('Not in your plan yet!', 'solidcam') .' ">';
			echo '            <i class="far fa-plus-circle"></i> ' . esc_html__('Get this module!', 'solidcam');
			echo '        </a>';
		}
		echo '    </div>';
		echo '    <div class="card-module-footer ' . esc_attr($footer_class) . '">';
		echo '        <a class="card-module-link" href="' . esc_url($link['url']) . '">';
		echo '            <i class="fal fa-info-circle" aria-hidden="true"></i> ' . esc_html__('More Information', 'solidcam');
		echo '        </a>';
		echo '    </div>';
		echo '</div>';
	}
}